# 修复data/Porgres.py中的缩进问题
with open("data/Porgres.py", "r", encoding="utf-8") as f:
    content = f.read()

# 修复第69行的缩进问题
fixed_content = content.replace(
    '            except Exception as e:\n                    logger.debug(f"从所有script标签获取jobEnc失败: {str(e)}")',
    '            except Exception as e:\n                logger.debug(f"从所有script标签获取jobEnc失败: {str(e)}")',
)

# 修复第97行的缩进问题
fixed_content = fixed_content.replace(
    '            except Exception as e:\n                    logger.debug(f"从URL参数提取pEnc失败: {str(e)}")',
    '            except Exception as e:\n                logger.debug(f"从URL参数提取pEnc失败: {str(e)}")',
)

# 保存修复后的文件
with open("data/Porgres.py", "w", encoding="utf-8") as f:
    f.write(fixed_content)

print("data/Porgres.py缩进问题已修复")
