2025-07-25 04:52:33.234 | INFO     | __main__:<module>:1888 - 学习通自动化系统启动...
2025-07-25 04:52:33.235 | SUCCESS  | Config.UserSql:__init__:22 - 数据库连接池初始化成功
2025-07-25 04:52:33.236 | SUCCESS  | __main__:<module>:1898 - 数据库连接池初始化成功
2025-07-25 04:52:33.778 | SUCCESS  | __main__:<module>:1919 - 数据状态重置完成
2025-07-25 04:52:33.778 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-25 04:52:33.779 | INFO     | __main__:<module>:1935 - 开始处理订单...
2025-07-25 04:52:33.779 | INFO     | __main__:order_get:1691 - 订单处理线程启动，最大线程数: 100
2025-07-25 04:52:34.191 | INFO     | __main__:order_get:1716 - 获取到 1 个待处理订单，当前活跃线程数: 0
2025-07-25 04:52:34.198 | INFO     | __main__:Run:1775 - 开始处理订单: OID=314481, 用户=13565793176, 课程ID=250795789|117127001|348369757|44196120|0|23cc9c511531c5135e1a52f7af7e2cb1
2025-07-25 04:52:34.198 | INFO     | __main__:register_thread:67 - 注册线程: OID=314481, 当前活跃线程数: 1
2025-07-25 04:52:34.849 | SUCCESS  | __main__:Run:1819 - ID:13565793176,登录成功
2025-07-25 04:52:34.931 | INFO     | __main__:kclist:225 - ID:13565793176,平台ID 9004，使用特殊处理方式获取作业信息
2025-07-25 04:52:34.932 | SUCCESS  | __main__:kclist:242 - ID:13565793176,成功构造作业课程信息: {'kcname': '作业任务', 'courseid': '250795789|117127001|348369757|44196120|0|23cc9c511531c5135e1a52f7af7e2cb1', 'clazzid': '250795789|117127001|348369757|44196120|0|23cc9c511531c5135e1a52f7af7e2cb1', 'cpi': ''}
2025-07-25 04:52:34.932 | SUCCESS  | __main__:Run:1828 - ID:13565793176,课程信息匹配成功
2025-07-25 04:52:35.094 | SUCCESS  | __main__:studentstudy:935 - ID:13565793176,进度:100%,详情:作业任务_44196120 | 进行中 | 进度: 1/1 | 更新: 2025-07-25 04:52:35
2025-07-25 04:52:36.369 | INFO     | __main__:_fallback_to_traditional_homework:1496 - ID:13565793176,获取到真实作业标题: 总体国家安全观测试题
2025-07-25 04:52:37.181 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1394 - ID:13565793176,提取到基本表单字段: courseId=250795789, classId=117127001, workId=44196120
2025-07-25 04:52:37.188 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1405 - ID:13565793176,找到 45 个题目项
2025-07-25 04:52:37.199 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1535 - ID:13565793176,成功提取到 45 个问题
2025-07-25 04:52:37.200 | ERROR    | API.HomeworkAI:process_homework:1025 - ID:13565793176,未生成任何答案
2025-07-25 04:52:37.880 | INFO     | API.WorkTask:Html_Wkrk:185 - ID:13565793176,尝试解析作业页面参数
2025-07-25 04:52:50.017 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 2014年4月15日
2025-07-25 04:52:50.019 | INFO     | API.WorkTask:Xuan:972 - ID:13565793176,找到选项: {'A': 'A2014年4月15日', 'B': 'B2015年7月1日', 'C': 'C2017年10月18日', 'D': 'D2018年4月15日'}
2025-07-25 04:52:50.020 | INFO     | API.WorkTask:Xuan:973 - ID:13565793176,题库答案: 2014年4月15日
2025-07-25 04:52:50.020 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:13565793176,答案与选项内容包含匹配: A
2025-07-25 04:52:50.020 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 1/45 个题目
2025-07-25 04:53:02.665 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 人民安全
2025-07-25 04:53:02.666 | INFO     | API.WorkTask:Xuan:972 - ID:13565793176,找到选项: {'A': 'A政治安全', 'B': 'B人民安全', 'C': 'C经济安全', 'D': 'D国际安全'}
2025-07-25 04:53:02.667 | INFO     | API.WorkTask:Xuan:973 - ID:13565793176,题库答案: 人民安全
2025-07-25 04:53:02.668 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:13565793176,答案与选项内容包含匹配: B
2025-07-25 04:53:02.668 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 2/45 个题目
2025-07-25 04:53:14.918 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 政治安全
2025-07-25 04:53:14.921 | INFO     | API.WorkTask:Xuan:972 - ID:13565793176,找到选项: {'A': 'A人民安全', 'B': 'B政治安全', 'C': 'C经济安全', 'D': 'D军事安全'}
2025-07-25 04:53:14.922 | INFO     | API.WorkTask:Xuan:973 - ID:13565793176,题库答案: 政治安全
2025-07-25 04:53:14.923 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:13565793176,答案与选项内容包含匹配: B
2025-07-25 04:53:14.924 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 3/45 个题目
2025-07-25 04:53:26.925 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 社交安全
2025-07-25 04:53:26.926 | INFO     | API.WorkTask:Xuan:972 - ID:13565793176,找到选项: {'A': 'A数据安全', 'B': 'B生物安全', 'C': 'C文化安全', 'D': 'D社交安全'}
2025-07-25 04:53:26.926 | INFO     | API.WorkTask:Xuan:973 - ID:13565793176,题库答案: 社交安全
2025-07-25 04:53:26.927 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:13565793176,答案与选项内容包含匹配: D
2025-07-25 04:53:26.927 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 4/45 个题目
2025-07-25 04:53:33.780 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-25 04:53:38.805 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 4月15日
2025-07-25 04:53:38.806 | INFO     | API.WorkTask:Xuan:972 - ID:13565793176,找到选项: {'A': 'A3月15日', 'B': 'B4月15日', 'C': 'C9月18日', 'D': 'D12月4日'}
2025-07-25 04:53:38.806 | INFO     | API.WorkTask:Xuan:973 - ID:13565793176,题库答案: 4月15日
2025-07-25 04:53:38.807 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:13565793176,答案与选项内容包含匹配: B
2025-07-25 04:53:38.807 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 5/45 个题目
2025-07-25 04:53:50.929 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 政治安全
2025-07-25 04:53:50.930 | INFO     | API.WorkTask:Xuan:972 - ID:13565793176,找到选项: {'A': 'A政治安全', 'B': 'B经济安全', 'C': 'C社会安全', 'D': 'D网络安全'}
2025-07-25 04:53:50.930 | INFO     | API.WorkTask:Xuan:973 - ID:13565793176,题库答案: 政治安全
2025-07-25 04:53:50.930 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:13565793176,答案与选项内容包含匹配: A
2025-07-25 04:53:50.930 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 6/45 个题目
2025-07-25 04:54:03.508 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 12339
2025-07-25 04:54:03.510 | INFO     | API.WorkTask:Xuan:972 - ID:13565793176,找到选项: {'A': 'A110', 'B': 'B12339', 'C': 'C12345', 'D': 'D12390'}
2025-07-25 04:54:03.511 | INFO     | API.WorkTask:Xuan:973 - ID:13565793176,题库答案: 12339
2025-07-25 04:54:03.511 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:13565793176,答案与选项内容包含匹配: B
2025-07-25 04:54:03.511 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 7/45 个题目
2025-07-25 04:54:16.566 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 将未公开的科研数据上传至云盘
2025-07-25 04:54:16.568 | INFO     | API.WorkTask:Xuan:972 - ID:13565793176,找到选项: {'A': 'A拒绝境外机构以"学术调研"名义索要个人信息', 'B': 'B将未公开的科研数据上传至云盘', 'C': 'C在校园内拍摄军训照片并分享到朋友圈', 'D': 'D向社区举报可疑的外籍人员活动'}
2025-07-25 04:54:16.568 | INFO     | API.WorkTask:Xuan:973 - ID:13565793176,题库答案: 将未公开的科研数据上传至云盘
2025-07-25 04:54:16.568 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:13565793176,答案与选项内容包含匹配: B
2025-07-25 04:54:16.569 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 8/45 个题目
2025-07-25 04:54:29.132 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 16个
2025-07-25 04:54:29.134 | INFO     | API.WorkTask:Xuan:972 - ID:13565793176,找到选项: {'A': 'A16个', 'B': 'B18个', 'C': 'C20个', 'D': 'D22个'}
2025-07-25 04:54:29.135 | INFO     | API.WorkTask:Xuan:973 - ID:13565793176,题库答案: 16个
2025-07-25 04:54:29.135 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:13565793176,答案与选项内容包含匹配: A
2025-07-25 04:54:29.136 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 9/45 个题目
2025-07-25 04:54:33.781 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-25 04:54:41.628 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 使用公共WiFi登录个人网银
2025-07-25 04:54:41.629 | INFO     | API.WorkTask:Xuan:972 - ID:13565793176,找到选项: {'A': 'A使用公共WiFi登录个人网银', 'B': 'B定期更新手机系统', 'C': 'C拒绝点击陌生邮件链接', 'D': 'D设置复杂密码保护账户'}
2025-07-25 04:54:41.630 | INFO     | API.WorkTask:Xuan:973 - ID:13565793176,题库答案: 使用公共WiFi登录个人网银
2025-07-25 04:54:41.631 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:13565793176,答案与选项内容包含匹配: A
2025-07-25 04:54:41.631 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 10/45 个题目
2025-07-25 04:54:54.099 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 保障人民幸福安康
2025-07-25 04:54:54.101 | INFO     | API.WorkTask:Xuan:972 - ID:13565793176,找到选项: {'A': 'A保障人民幸福安康', 'B': 'B维护国家主权', 'C': 'C促进经济发展', 'D': 'D提升国际地位'}
2025-07-25 04:54:54.101 | INFO     | API.WorkTask:Xuan:973 - ID:13565793176,题库答案: 保障人民幸福安康
2025-07-25 04:54:54.101 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:13565793176,答案与选项内容包含匹配: A
2025-07-25 04:54:54.102 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 11/45 个题目
2025-07-25 04:55:06.822 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 规范校园外卖食品安全
2025-07-25 04:55:06.824 | INFO     | API.WorkTask:Xuan:972 - ID:13565793176,找到选项: {'A': 'A保护非物质文化遗产', 'B': 'B抵制不良网络文化入侵', 'C': 'C规范校园外卖食品安全', 'D': 'D防范西方意识形态渗透'}
2025-07-25 04:55:06.824 | INFO     | API.WorkTask:Xuan:973 - ID:13565793176,题库答案: 规范校园外卖食品安全
2025-07-25 04:55:06.824 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:13565793176,答案与选项内容包含匹配: C
2025-07-25 04:55:06.825 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 12/45 个题目
2025-07-25 04:55:18.777 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 风险防控
2025-07-25 04:55:18.778 | INFO     | API.WorkTask:Xuan:972 - ID:13565793176,找到选项: {'A': 'A风险防控', 'B': 'B人民利益', 'C': 'C科技创新', 'D': 'D国际合作'}
2025-07-25 04:55:18.778 | INFO     | API.WorkTask:Xuan:973 - ID:13565793176,题库答案: 风险防控
2025-07-25 04:55:18.779 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:13565793176,答案与选项内容包含匹配: A
2025-07-25 04:55:18.779 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 13/45 个题目
2025-07-25 04:55:30.572 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 实验数据保密
2025-07-25 04:55:30.574 | INFO     | API.WorkTask:Xuan:972 - ID:13565793176,找到选项: {'A': 'A实验数据保密', 'B': 'B设备使用时长', 'C': 'C实验报告格式', 'D': 'D实验耗材成本'}
2025-07-25 04:55:30.574 | INFO     | API.WorkTask:Xuan:973 - ID:13565793176,题库答案: 实验数据保密
2025-07-25 04:55:30.574 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:13565793176,答案与选项内容包含匹配: A
2025-07-25 04:55:30.575 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 14/45 个题目
2025-07-25 04:55:33.784 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-25 04:55:43.481 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 转基因食品一定危害国家安全
2025-07-25 04:55:43.482 | INFO     | API.WorkTask:Xuan:972 - ID:13565793176,找到选项: {'A': 'A随意放生外来物种可能破坏生态平衡', 'B': 'B实验室生物样本需按规定处理', 'C': 'C转基因食品一定危害国家安全', 'D': 'D防范生物入侵是生物安全重要内容'}
2025-07-25 04:55:43.482 | INFO     | API.WorkTask:Xuan:973 - ID:13565793176,题库答案: 转基因食品一定危害国家安全
2025-07-25 04:55:43.483 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:13565793176,答案与选项内容包含匹配: C
2025-07-25 04:55:43.483 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 15/45 个题目
2025-07-25 04:55:55.770 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 中国共产党
2025-07-25 04:55:55.771 | INFO     | API.WorkTask:Xuan:972 - ID:13565793176,找到选项: {'A': 'A中国共产党', 'B': 'B国务院', 'C': 'C人民代表大会', 'D': 'D中央军委'}
2025-07-25 04:55:55.771 | INFO     | API.WorkTask:Xuan:973 - ID:13565793176,题库答案: 中国共产党
2025-07-25 04:55:55.772 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:13565793176,答案与选项内容包含匹配: A
2025-07-25 04:55:55.772 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 16/45 个题目
2025-07-25 04:56:08.373 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 及时更新手机系统补丁
2025-07-25 04:56:08.375 | INFO     | API.WorkTask:Xuan:972 - ID:13565793176,找到选项: {'A': 'A在军事禁区附近拍摄并发布到网络', 'B': 'B参与境外机构组织的敏感信息调研', 'C': 'C及时更新手机系统补丁', 'D': 'D将涉密文件拍照发送给同学'}
2025-07-25 04:56:08.375 | INFO     | API.WorkTask:Xuan:973 - ID:13565793176,题库答案: 及时更新手机系统补丁
2025-07-25 04:56:08.375 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:13565793176,答案与选项内容包含匹配: C
2025-07-25 04:56:08.376 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 17/45 个题目
2025-07-25 04:56:20.645 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 全社会共同责任
2025-07-25 04:56:20.648 | INFO     | API.WorkTask:Xuan:972 - ID:13565793176,找到选项: {'A': 'A一时之事', 'B': 'B局部之事', 'C': 'C全社会共同责任', 'D': 'D政府部门的专属职责'}
2025-07-25 04:56:20.648 | INFO     | API.WorkTask:Xuan:973 - ID:13565793176,题库答案: 全社会共同责任
2025-07-25 04:56:20.649 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:13565793176,答案与选项内容包含匹配: C
2025-07-25 04:56:20.649 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 18/45 个题目
2025-07-25 04:56:32.554 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 禁止使用进口商品
2025-07-25 04:56:32.556 | INFO     | API.WorkTask:Xuan:972 - ID:13565793176,找到选项: {'A': 'A防范金融风险', 'B': 'B保护知识产权', 'C': 'C维护粮食安全', 'D': 'D禁止使用进口商品'}
2025-07-25 04:56:32.557 | INFO     | API.WorkTask:Xuan:973 - ID:13565793176,题库答案: 禁止使用进口商品
2025-07-25 04:56:32.557 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:13565793176,答案与选项内容包含匹配: D
2025-07-25 04:56:32.558 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 19/45 个题目
2025-07-25 04:56:33.785 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-25 04:56:44.905 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 主动宣传我国政策主张
2025-07-25 04:56:44.907 | INFO     | API.WorkTask:Xuan:972 - ID:13565793176,找到选项: {'A': 'A随意透露国家机密信息', 'B': 'B主动宣传我国政策主张', 'C': 'C拒绝与外籍人员交流', 'D': 'D隐瞒个人真实身份信息'}
2025-07-25 04:56:44.907 | INFO     | API.WorkTask:Xuan:973 - ID:13565793176,题库答案: 主动宣传我国政策主张
2025-07-25 04:56:44.907 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:13565793176,答案与选项内容包含匹配: B
2025-07-25 04:56:44.908 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 20/45 个题目
2025-07-25 04:56:57.340 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 政治安全
网络安全
生态安全
资源安全
2025-07-25 04:56:57.341 | INFO     | API.WorkTask:get_ai_answer_for_choice:1784 - ID:13565793176,检测到换行符分隔的多选题答案，转换为###分隔
2025-07-25 04:56:57.342 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 21/45 个题目
2025-07-25 04:57:10.016 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 学习国家安全知识
举报可疑行为
参与国家安全宣传活动
加强个人信息保护
2025-07-25 04:57:10.016 | INFO     | API.WorkTask:get_ai_answer_for_choice:1784 - ID:13565793176,检测到换行符分隔的多选题答案，转换为###分隔
2025-07-25 04:57:10.017 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 22/45 个题目
2025-07-25 04:57:22.095 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 非法获取国家秘密
煽动分裂国家
泄露军事设施信息
传播危害国家安全的网络谣言
2025-07-25 04:57:22.096 | INFO     | API.WorkTask:get_ai_answer_for_choice:1784 - ID:13565793176,检测到换行符分隔的多选题答案，转换为###分隔
2025-07-25 04:57:22.096 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 23/45 个题目
2025-07-25 04:57:33.785 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-25 04:57:34.969 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 不随意点击陌生链接
不传播未经证实的敏感信息
定期更换密码
谨慎使用公共网络
2025-07-25 04:57:34.970 | INFO     | API.WorkTask:get_ai_answer_for_choice:1784 - ID:13565793176,检测到换行符分隔的多选题答案，转换为###分隔
2025-07-25 04:57:34.971 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 24/45 个题目
2025-07-25 04:57:47.757 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 外部安全和内部安全
国土安全和国民安全
传统安全和非传统安全
自身安全和共同安全
2025-07-25 04:57:47.757 | INFO     | API.WorkTask:get_ai_answer_for_choice:1784 - ID:13565793176,检测到换行符分隔的多选题答案，转换为###分隔
2025-07-25 04:57:47.758 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 25/45 个题目
2025-07-25 04:58:01.039 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 抵御文化渗透
传承民族文化
维护意识形态安全
增强国家文化软实力
2025-07-25 04:58:01.039 | INFO     | API.WorkTask:get_ai_answer_for_choice:1784 - ID:13565793176,检测到换行符分隔的多选题答案，转换为###分隔
2025-07-25 04:58:01.041 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 26/45 个题目
2025-07-25 04:58:13.103 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 个人信息泄露可能威胁国家安全
科研数据需按保密规定管理
数据跨境传输需遵守法律规定
2025-07-25 04:58:13.104 | INFO     | API.WorkTask:get_ai_answer_for_choice:1784 - ID:13565793176,检测到换行符分隔的多选题答案，转换为###分隔
2025-07-25 04:58:13.104 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 27/45 个题目
2025-07-25 04:58:25.539 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 不泄露国家敏感信息
遵守外事纪律
客观宣传中国发展成就
警惕境外机构的可疑调研
2025-07-25 04:58:25.540 | INFO     | API.WorkTask:get_ai_answer_for_choice:1784 - ID:13565793176,检测到换行符分隔的多选题答案，转换为###分隔
2025-07-25 04:58:25.540 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 28/45 个题目
2025-07-25 04:58:33.786 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-25 04:58:38.162 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 保障粮食安全
防范金融风险
维护产业安全
促进数字经济健康发展
2025-07-25 04:58:38.162 | INFO     | API.WorkTask:get_ai_answer_for_choice:1784 - ID:13565793176,检测到换行符分隔的多选题答案，转换为###分隔
2025-07-25 04:58:38.163 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 29/45 个题目
2025-07-25 04:58:50.341 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:13565793176,AI返回的答案内容: 《中华人民共和国国家安全法》
《反间谍法》
《网络安全法》
《数据安全法》
2025-07-25 04:58:50.342 | INFO     | API.WorkTask:get_ai_answer_for_choice:1784 - ID:13565793176,检测到换行符分隔的多选题答案，转换为###分隔
2025-07-25 04:58:50.342 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 30/45 个题目
2025-07-25 04:59:02.752 | SUCCESS  | API.WorkTask:Html_Wkrk:752 - ID:13565793176,AI生成判断题答案成功
2025-07-25 04:59:02.752 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 31/45 个题目
2025-07-25 04:59:15.477 | SUCCESS  | API.WorkTask:Html_Wkrk:752 - ID:13565793176,AI生成判断题答案成功
2025-07-25 04:59:15.478 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 32/45 个题目
2025-07-25 04:59:27.863 | SUCCESS  | API.WorkTask:Html_Wkrk:752 - ID:13565793176,AI生成判断题答案成功
2025-07-25 04:59:27.863 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 33/45 个题目
2025-07-25 04:59:33.787 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-25 04:59:40.305 | SUCCESS  | API.WorkTask:Html_Wkrk:752 - ID:13565793176,AI生成判断题答案成功
2025-07-25 04:59:40.305 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 34/45 个题目
2025-07-25 04:59:52.792 | SUCCESS  | API.WorkTask:Html_Wkrk:752 - ID:13565793176,AI生成判断题答案成功
2025-07-25 04:59:52.793 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 35/45 个题目
2025-07-25 05:00:05.391 | SUCCESS  | API.WorkTask:Html_Wkrk:752 - ID:13565793176,AI生成判断题答案成功
2025-07-25 05:00:05.391 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 36/45 个题目
2025-07-25 05:00:17.553 | SUCCESS  | API.WorkTask:Html_Wkrk:752 - ID:13565793176,AI生成判断题答案成功
2025-07-25 05:00:17.553 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 37/45 个题目
2025-07-25 05:00:29.680 | SUCCESS  | API.WorkTask:Html_Wkrk:752 - ID:13565793176,AI生成判断题答案成功
2025-07-25 05:00:29.681 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 38/45 个题目
2025-07-25 05:00:33.788 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-25 05:00:42.036 | SUCCESS  | API.WorkTask:Html_Wkrk:752 - ID:13565793176,AI生成判断题答案成功
2025-07-25 05:00:42.039 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 39/45 个题目
2025-07-25 05:00:54.149 | SUCCESS  | API.WorkTask:Html_Wkrk:752 - ID:13565793176,AI生成判断题答案成功
2025-07-25 05:00:54.150 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 40/45 个题目
2025-07-25 05:01:06.220 | SUCCESS  | API.WorkTask:Html_Wkrk:752 - ID:13565793176,AI生成判断题答案成功
2025-07-25 05:01:06.220 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 41/45 个题目
2025-07-25 05:01:18.940 | SUCCESS  | API.WorkTask:Html_Wkrk:752 - ID:13565793176,AI生成判断题答案成功
2025-07-25 05:01:18.940 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 42/45 个题目
2025-07-25 05:01:30.923 | SUCCESS  | API.WorkTask:Html_Wkrk:752 - ID:13565793176,AI生成判断题答案成功
2025-07-25 05:01:30.924 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 43/45 个题目
2025-07-25 05:01:33.793 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-25 05:01:43.125 | SUCCESS  | API.WorkTask:Html_Wkrk:752 - ID:13565793176,AI生成判断题答案成功
2025-07-25 05:01:43.125 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 44/45 个题目
2025-07-25 05:01:55.258 | SUCCESS  | API.WorkTask:Html_Wkrk:752 - ID:13565793176,AI生成判断题答案成功
2025-07-25 05:01:55.258 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:13565793176,已处理 45/45 个题目
2025-07-25 05:01:55.259 | INFO     | API.WorkTask:Html_Wkrk:839 - ID:13565793176,所有题目处理完成，共 45/45 个
2025-07-25 05:01:55.260 | INFO     | API.WorkTask:PostDo:1385 - ID:13565793176,AI答题比例: 100.00%, AI答题数量: 45, 题库答题比例: 0.00%, 题库答题数量: 0, 总题目数量: 45
2025-07-25 05:01:55.260 | INFO     | API.WorkTask:PostDo:1410 - ID:13565793176,题库答题比例:0.00%, 使用保存模式
2025-07-25 05:01:56.533 | SUCCESS  | API.WorkTask:PostDo:1454 - ID:13565793176,保存答案成功: {"msg":"保存成功！","status":true}
2025-07-25 05:01:56.540 | INFO     | API.WorkTask:_update_progress_info:1542 - ID:13565793176,尝试导入data.Porgres模块
2025-07-25 05:01:56.541 | INFO     | API.WorkTask:_update_progress_info:1548 - ID:13565793176,成功导入data.Porgres.StartProces
2025-07-25 05:01:56.542 | INFO     | API.WorkTask:_update_progress_info:1570 - ID:13565793176,创建StartProces对象
2025-07-25 05:01:56.545 | INFO     | API.WorkTask:_update_progress_info:1583 - ID:13565793176,调用get_platform_9004_progress方法
2025-07-25 05:01:56.547 | INFO     | API.WorkTask:_update_progress_info:1595 - ID:13565793176,进度更新: 100%, 总体国家安全观测试题 (已保存) | 题库答题比例:0% | AI答题比例:100%，使用保存模式 | 进度: 1/1 | 更新: 2025-07-25 05:01:56 | 题库答题比例:0% | AI答题比例:100%，使用保存模式
2025-07-25 05:01:56.547 | SUCCESS  | Config.UserSql:__init__:22 - 数据库连接池初始化成功
2025-07-25 05:01:56.552 | INFO     | API.WorkTask:_update_progress_info:1603 - ID:13565793176,更新数据库记录, oid: 314481
2025-07-25 05:01:56.801 | SUCCESS  | __main__:_fallback_to_traditional_homework:1611 - ID:13565793176,进度:100%,详情:总体国家安全观测试题 | 题库答题比例:0% | AI答题比例:100%，使用保存模式 | 进度: 1/1 | 更新: 2025-07-25 05:01:56
2025-07-25 05:01:56.804 | SUCCESS  | __main__:_fallback_to_traditional_homework:1626 - ID:13565793176,传统方法作业任务 总体国家安全观测试题 处理成功
2025-07-25 05:01:56.807 | INFO     | __main__:studentstudy:1050 - ID:13565793176,作业任务 作业任务_44196120 完成，等待 6 秒后处理下一任务
2025-07-25 05:02:02.892 | INFO     | __main__:unregister_thread:74 - 注销线程: OID=314481, 当前活跃线程数: 0
2025-07-25 05:02:33.794 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-25 05:03:00.418 | WARNING  | __main__:signal_handler:104 - 接收到终止信号，正在安全关闭...
2025-07-25 05:03:00.418 | INFO     | __main__:<module>:1945 - 程序退出
