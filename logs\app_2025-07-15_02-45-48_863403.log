2025-07-15 02:45:48.864 | INFO     | __main__:<module>:1888 - 学习通自动化系统启动...
2025-07-15 02:45:48.865 | SUCCESS  | Config.UserSql:__init__:22 - 数据库连接池初始化成功
2025-07-15 02:45:48.865 | SUCCESS  | __main__:<module>:1898 - 数据库连接池初始化成功
2025-07-15 02:45:48.868 | SUCCESS  | __main__:<module>:1919 - 数据状态重置完成
2025-07-15 02:45:48.869 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 02:45:48.869 | INFO     | __main__:<module>:1935 - 开始处理订单...
2025-07-15 02:45:48.869 | INFO     | __main__:order_get:1691 - 订单处理线程启动，最大线程数: 100
2025-07-15 02:45:48.872 | INFO     | __main__:order_get:1716 - 获取到 1 个待处理订单，当前活跃线程数: 0
2025-07-15 02:45:48.873 | INFO     | __main__:Run:1775 - 开始处理订单: OID=300001, 用户=17328022636, 课程ID=246628486|107051161|352747128|42670108|6a76f288656266a67fb6a4820a3e7864|5ac17897c0eea6971f688e43b15b8f51
2025-07-15 02:45:48.873 | INFO     | __main__:register_thread:67 - 注册线程: OID=300001, 当前活跃线程数: 1
2025-07-15 02:45:49.446 | SUCCESS  | __main__:Run:1819 - ID:17328022636,登录成功
2025-07-15 02:45:49.455 | INFO     | __main__:kclist:225 - ID:17328022636,平台ID 9004，使用特殊处理方式获取作业信息
2025-07-15 02:45:49.455 | SUCCESS  | __main__:kclist:242 - ID:17328022636,成功构造作业课程信息: {'kcname': '作业任务', 'courseid': '246628486|107051161|352747128|42670108|6a76f288656266a67fb6a4820a3e7864|5ac17897c0eea6971f688e43b15b8f51', 'clazzid': '246628486|107051161|352747128|42670108|6a76f288656266a67fb6a4820a3e7864|5ac17897c0eea6971f688e43b15b8f51', 'cpi': ''}
2025-07-15 02:45:49.456 | SUCCESS  | __main__:Run:1828 - ID:17328022636,课程信息匹配成功
2025-07-15 02:45:49.460 | SUCCESS  | __main__:studentstudy:935 - ID:17328022636,进度:100%,详情:作业任务_42670108 | 进行中 | 进度: 1/1 | 更新: 2025-07-15 02:45:49
2025-07-15 02:45:50.790 | INFO     | __main__:_fallback_to_traditional_homework:1496 - ID:17328022636,获取到真实作业标题: 第十五章 第一二节
2025-07-15 02:45:51.534 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1394 - ID:17328022636,提取到基本表单字段: courseId=246628486, classId=107051161, workId=42670108
2025-07-15 02:45:51.538 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1405 - ID:17328022636,找到 22 个题目项
2025-07-15 02:45:51.541 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1515 - ID:17328022636,成功提取到 22 个问题
2025-07-15 02:45:51.542 | ERROR    | API.HomeworkAI:process_homework:1025 - ID:17328022636,未生成任何答案
2025-07-15 02:45:52.198 | INFO     | API.WorkTask:Html_Wkrk:185 - ID:17328022636,尝试解析作业页面参数
2025-07-15 02:46:18.272 | INFO     | API.WorkTask:get_ai_answer_for_choice:1703 - ID:17328022636,AI返回的答案内容: 教育
2025-07-15 02:46:18.274 | INFO     | API.WorkTask:Xuan:968 - ID:17328022636,找到选项: {'A': 'A教育', 'B': 'B科技', 'C': 'C人才', 'D': 'D创新'}
2025-07-15 02:46:18.274 | INFO     | API.WorkTask:Xuan:969 - ID:17328022636,题库答案: 教育
2025-07-15 02:46:18.275 | SUCCESS  | API.WorkTask:Xuan:1027 - ID:17328022636,答案与选项内容包含匹配: A
2025-07-15 02:46:18.275 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 1/22 个题目
2025-07-15 02:46:46.382 | INFO     | API.WorkTask:get_ai_answer_for_choice:1703 - ID:17328022636,AI返回的答案内容: 科技
2025-07-15 02:46:46.384 | INFO     | API.WorkTask:Xuan:968 - ID:17328022636,找到选项: {'A': 'A教育', 'B': 'B科技', 'C': 'C人才', 'D': 'D创新'}
2025-07-15 02:46:46.385 | INFO     | API.WorkTask:Xuan:969 - ID:17328022636,题库答案: 科技
2025-07-15 02:46:46.386 | SUCCESS  | API.WorkTask:Xuan:1027 - ID:17328022636,答案与选项内容包含匹配: B
2025-07-15 02:46:46.386 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 2/22 个题目
2025-07-15 02:46:48.870 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 02:47:11.242 | INFO     | API.WorkTask:get_ai_answer_for_choice:1703 - ID:17328022636,AI返回的答案内容: 教育
2025-07-15 02:47:11.244 | INFO     | API.WorkTask:Xuan:968 - ID:17328022636,找到选项: {'A': 'A教育', 'B': 'B科技', 'C': 'C人才', 'D': 'D创新'}
2025-07-15 02:47:11.244 | INFO     | API.WorkTask:Xuan:969 - ID:17328022636,题库答案: 教育
2025-07-15 02:47:11.246 | SUCCESS  | API.WorkTask:Xuan:1027 - ID:17328022636,答案与选项内容包含匹配: A
2025-07-15 02:47:11.246 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 3/22 个题目
2025-07-15 02:47:48.872 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 02:47:49.528 | WARNING  | API.WorkTask:get_ai_answer_for_choice:1753 - ID:17328022636,AI接口返回错误: API请求失败: HTTP码=0
2025-07-15 02:47:49.530 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 4/22 个题目
2025-07-15 02:48:15.452 | INFO     | API.WorkTask:get_ai_answer_for_choice:1703 - ID:17328022636,AI返回的答案内容: 人才
2025-07-15 02:48:15.453 | INFO     | API.WorkTask:Xuan:968 - ID:17328022636,找到选项: {'A': 'A科技', 'B': 'B人才', 'C': 'C创新', 'D': 'D教育'}
2025-07-15 02:48:15.454 | INFO     | API.WorkTask:Xuan:969 - ID:17328022636,题库答案: 人才
2025-07-15 02:48:15.454 | SUCCESS  | API.WorkTask:Xuan:1027 - ID:17328022636,答案与选项内容包含匹配: B
2025-07-15 02:48:15.455 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 5/22 个题目
2025-07-15 02:48:40.972 | INFO     | API.WorkTask:get_ai_answer_for_choice:1703 - ID:17328022636,AI返回的答案内容: 创新
2025-07-15 02:48:40.974 | INFO     | API.WorkTask:Xuan:968 - ID:17328022636,找到选项: {'A': 'A科技', 'B': 'B人才', 'C': 'C创新', 'D': 'D教育'}
2025-07-15 02:48:40.974 | INFO     | API.WorkTask:Xuan:969 - ID:17328022636,题库答案: 创新
2025-07-15 02:48:40.974 | SUCCESS  | API.WorkTask:Xuan:1027 - ID:17328022636,答案与选项内容包含匹配: C
2025-07-15 02:48:40.975 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 6/22 个题目
2025-07-15 02:48:48.874 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 02:49:05.444 | INFO     | API.WorkTask:get_ai_answer_for_choice:1703 - ID:17328022636,AI返回的答案内容: 教育优先发展
2025-07-15 02:49:05.446 | INFO     | API.WorkTask:Xuan:968 - ID:17328022636,找到选项: {'A': 'A创新驱动发展', 'B': 'B科技自立自强', 'C': 'C人才引领驱动', 'D': 'D教育优先发展'}
2025-07-15 02:49:05.447 | INFO     | API.WorkTask:Xuan:969 - ID:17328022636,题库答案: 教育优先发展
2025-07-15 02:49:05.447 | SUCCESS  | API.WorkTask:Xuan:1027 - ID:17328022636,答案与选项内容包含匹配: D
2025-07-15 02:49:05.448 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 7/22 个题目
2025-07-15 02:49:31.158 | INFO     | API.WorkTask:get_ai_answer_for_choice:1703 - ID:17328022636,AI返回的答案内容: 科技自立自强
2025-07-15 02:49:31.160 | INFO     | API.WorkTask:Xuan:968 - ID:17328022636,找到选项: {'A': 'A创新驱动发展', 'B': 'B科技自立自强', 'C': 'C人才引领驱动', 'D': 'D教育优先发展'}
2025-07-15 02:49:31.161 | INFO     | API.WorkTask:Xuan:969 - ID:17328022636,题库答案: 科技自立自强
2025-07-15 02:49:31.161 | SUCCESS  | API.WorkTask:Xuan:1027 - ID:17328022636,答案与选项内容包含匹配: B
2025-07-15 02:49:31.162 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 8/22 个题目
2025-07-15 02:49:48.875 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 02:50:09.254 | WARNING  | API.WorkTask:get_ai_answer_for_choice:1753 - ID:17328022636,AI接口返回错误: API请求失败: HTTP码=0
2025-07-15 02:50:09.255 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 9/22 个题目
2025-07-15 02:50:34.184 | INFO     | API.WorkTask:get_ai_answer_for_choice:1703 - ID:17328022636,AI返回的答案内容: 坚持党对教育工作的全面领导
2025-07-15 02:50:34.185 | INFO     | API.WorkTask:Xuan:968 - ID:17328022636,找到选项: {'A': 'A坚持党对教育工作的全面领导', 'B': 'B坚持社会主义教育方向', 'C': 'C坚持中国特色社会主义教育道路', 'D': 'D坚持立德树人根本任务'}
2025-07-15 02:50:34.186 | INFO     | API.WorkTask:Xuan:969 - ID:17328022636,题库答案: 坚持党对教育工作的全面领导
2025-07-15 02:50:34.187 | SUCCESS  | API.WorkTask:Xuan:1027 - ID:17328022636,答案与选项内容包含匹配: A
2025-07-15 02:50:34.188 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 10/22 个题目
2025-07-15 02:50:48.876 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 02:50:58.950 | INFO     | API.WorkTask:get_ai_answer_for_choice:1703 - ID:17328022636,AI返回的答案内容: 党的教育方针
2025-07-15 02:50:58.951 | INFO     | API.WorkTask:Xuan:968 - ID:17328022636,找到选项: {'A': 'A科教兴国战略', 'B': 'B人才强国战略', 'C': 'C社会主义教育方针', 'D': 'D党的教育方针'}
2025-07-15 02:50:58.952 | INFO     | API.WorkTask:Xuan:969 - ID:17328022636,题库答案: 党的教育方针
2025-07-15 02:50:58.953 | SUCCESS  | API.WorkTask:Xuan:1027 - ID:17328022636,答案与选项内容包含匹配: D
2025-07-15 02:50:58.954 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 11/22 个题目
2025-07-15 02:51:25.996 | INFO     | API.WorkTask:get_ai_answer_for_choice:1703 - ID:17328022636,AI返回的答案内容: 立德树人
2025-07-15 02:51:25.999 | INFO     | API.WorkTask:Xuan:968 - ID:17328022636,找到选项: {'A': 'A发展中国特色社会主义教育', 'B': 'B立德树人', 'C': 'C巩固和发展社会主义制度', 'D': 'D教育公平'}
2025-07-15 02:51:26.000 | INFO     | API.WorkTask:Xuan:969 - ID:17328022636,题库答案: 立德树人
2025-07-15 02:51:26.002 | SUCCESS  | API.WorkTask:Xuan:1027 - ID:17328022636,答案与选项内容包含匹配: B
2025-07-15 02:51:26.003 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 12/22 个题目
2025-07-15 02:51:48.877 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 02:51:52.256 | INFO     | API.WorkTask:get_ai_answer_for_choice:1703 - ID:17328022636,AI返回的答案内容: 思想政治理论课
2025-07-15 02:51:52.259 | INFO     | API.WorkTask:Xuan:968 - ID:17328022636,找到选项: {'A': 'A基础文化理论课', 'B': 'B劳动技术教育课', 'C': 'C思想政治理论课', 'D': 'D社会公德教育课'}
2025-07-15 02:51:52.260 | INFO     | API.WorkTask:Xuan:969 - ID:17328022636,题库答案: 思想政治理论课
2025-07-15 02:51:52.261 | SUCCESS  | API.WorkTask:Xuan:1027 - ID:17328022636,答案与选项内容包含匹配: C
2025-07-15 02:51:52.262 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 13/22 个题目
2025-07-15 02:52:17.878 | INFO     | API.WorkTask:get_ai_answer_for_choice:1703 - ID:17328022636,AI返回的答案内容: 坚持党对教育工作的全面领导
2025-07-15 02:52:17.879 | INFO     | API.WorkTask:Xuan:968 - ID:17328022636,找到选项: {'A': 'A坚持党对教育工作的全面领导', 'B': 'B坚持中国特色社会主义教育道路', 'C': 'C培养理想信念坚定的社会主义建设者和接班人', 'D': 'D培养德才兼备的社会公民'}
2025-07-15 02:52:17.881 | INFO     | API.WorkTask:Xuan:969 - ID:17328022636,题库答案: 坚持党对教育工作的全面领导
2025-07-15 02:52:17.881 | SUCCESS  | API.WorkTask:Xuan:1027 - ID:17328022636,答案与选项内容包含匹配: A
2025-07-15 02:52:17.881 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 14/22 个题目
2025-07-15 02:52:42.561 | INFO     | API.WorkTask:get_ai_answer_for_choice:1703 - ID:17328022636,AI返回的答案内容: 高质量发展
2025-07-15 02:52:42.563 | INFO     | API.WorkTask:Xuan:968 - ID:17328022636,找到选项: {'A': 'A高质量发展', 'B': 'B立德树人', 'C': 'C教育优先发展', 'D': 'D人民满意'}
2025-07-15 02:52:42.564 | INFO     | API.WorkTask:Xuan:969 - ID:17328022636,题库答案: 高质量发展
2025-07-15 02:52:42.564 | SUCCESS  | API.WorkTask:Xuan:1027 - ID:17328022636,答案与选项内容包含匹配: A
2025-07-15 02:52:42.565 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 15/22 个题目
2025-07-15 02:52:48.879 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 02:53:08.198 | INFO     | API.WorkTask:get_ai_answer_for_choice:1703 - ID:17328022636,AI返回的答案内容: 加强教师队伍建设
2025-07-15 02:53:08.200 | INFO     | API.WorkTask:Xuan:968 - ID:17328022636,找到选项: {'A': 'A坚定不移推进教育公平', 'B': 'B构建全社会共同育人格局', 'C': 'C加强教育基础设施建设', 'D': 'D加强教师队伍建设'}
2025-07-15 02:53:08.200 | INFO     | API.WorkTask:Xuan:969 - ID:17328022636,题库答案: 加强教师队伍建设
2025-07-15 02:53:08.200 | SUCCESS  | API.WorkTask:Xuan:1027 - ID:17328022636,答案与选项内容包含匹配: D
2025-07-15 02:53:08.201 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 16/22 个题目
2025-07-15 02:53:33.267 | INFO     | API.WorkTask:get_ai_answer_for_choice:1703 - ID:17328022636,AI返回的答案内容: 科技
人才
教育
2025-07-15 02:53:33.268 | INFO     | API.WorkTask:Xuan:968 - ID:17328022636,找到选项: {'A': 'A科技', 'B': 'B人才', 'C': 'C创新', 'D': 'D教育'}
2025-07-15 02:53:33.269 | INFO     | API.WorkTask:Xuan:969 - ID:17328022636,题库答案: 科技
人才
教育
2025-07-15 02:53:33.269 | INFO     | API.WorkTask:Xuan:1066 - ID:17328022636,尝试使用相似度匹配
2025-07-15 02:53:33.270 | INFO     | API.WorkTask:Xuan:1086 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'科技', '教育', '人才'}
2025-07-15 02:53:33.274 | INFO     | API.WorkTask:Xuan:1135 - ID:17328022636,尝试使用短语匹配，答案短语: ['科技\n人才\n教育']
2025-07-15 02:53:33.275 | ERROR    | API.WorkTask:Xuan:1243 - ID:17328022636,使用match_answer函数匹配失败: match_answer() missing 2 required positional arguments: 'qtp' and 'username'
2025-07-15 02:53:33.275 | WARNING  | API.WorkTask:Xuan:1247 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 02:53:33.276 | WARNING  | API.WorkTask:get_ai_answer_for_choice:1747 - ID:17328022636,AI回答无法匹配任何选项: 科技
人才
教育
2025-07-15 02:53:33.276 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 17/22 个题目
2025-07-15 02:53:48.880 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 02:54:07.885 | INFO     | API.WorkTask:get_ai_answer_for_choice:1703 - ID:17328022636,AI返回的答案内容: 我国教育现代化发展总体水平跨入世界中上国家行列
我国已建成世界上规模最大的教育体系
我国已经发展成为全球规模最宏大的人才资源大国
2025-07-15 02:54:07.888 | INFO     | API.WorkTask:Xuan:968 - ID:17328022636,找到选项: {'A': 'A我国教育现代化发展总体水平跨入世界中上国家行列', 'B': 'B我国进入创新型强国行首', 'C': 'C我国已建成世界上规模最大的教育体系', 'D': 'D我国已经发展成为全球规模最宏大的人才资源大国'}
2025-07-15 02:54:07.889 | INFO     | API.WorkTask:Xuan:969 - ID:17328022636,题库答案: 我国教育现代化发展总体水平跨入世界中上国家行列
我国已建成世界上规模最大的教育体系
我国已经发展成为全球规模最宏大的人才资源大国
2025-07-15 02:54:07.889 | INFO     | API.WorkTask:Xuan:1066 - ID:17328022636,尝试使用相似度匹配
2025-07-15 02:54:07.892 | INFO     | API.WorkTask:Xuan:1086 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'我国已经发展成为全球规模最宏大的人才资源大国', '我国教育现代化发展总体水平跨入世界中上国家行列', '我国已建成世界上规模最大的教育体系'}
2025-07-15 02:54:07.897 | INFO     | API.WorkTask:Xuan:1135 - ID:17328022636,尝试使用短语匹配，答案短语: ['我国教育现代化发展总体水平跨入世界中上国家行列\n我国已建成世界上规模最大的教育体系\n我国已经发展成为全球规模最宏大的人才资源大国']
2025-07-15 02:54:07.898 | ERROR    | API.WorkTask:Xuan:1243 - ID:17328022636,使用match_answer函数匹配失败: match_answer() missing 2 required positional arguments: 'qtp' and 'username'
2025-07-15 02:54:07.899 | WARNING  | API.WorkTask:Xuan:1247 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 02:54:07.900 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 18/22 个题目
2025-07-15 02:54:33.347 | INFO     | API.WorkTask:get_ai_answer_for_choice:1703 - ID:17328022636,AI返回的答案内容: 教育优先发展重在夯实人力资源深度开发基础
科技自立自强重在坚持独立自主开拓创新
人才引领驱动重在巩固发展优势赢得竞争主动
三者都紧紧围绕全面建设社会主义现代化国家战略需求
2025-07-15 02:54:33.349 | INFO     | API.WorkTask:Xuan:968 - ID:17328022636,找到选项: {'A': 'A教育优先发展重在夯实人力资源深度开发基础', 'B': 'B科技自立自强重在坚持独立自主开拓创新', 'C': 'C人才引领驱动重在巩固发展优势赢得竞争主动', 'D': 'D三者都紧紧围绕全面建设社会主义现代化国家战略需求'}
2025-07-15 02:54:33.350 | INFO     | API.WorkTask:Xuan:969 - ID:17328022636,题库答案: 教育优先发展重在夯实人力资源深度开发基础
科技自立自强重在坚持独立自主开拓创新
人才引领驱动重在巩固发展优势赢得竞争主动
三者都紧紧围绕全面建设社会主义现代化国家战略需求
2025-07-15 02:54:33.351 | INFO     | API.WorkTask:Xuan:1066 - ID:17328022636,尝试使用相似度匹配
2025-07-15 02:54:33.352 | INFO     | API.WorkTask:Xuan:1086 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'教育优先发展重在夯实人力资源深度开发基础', '三者都紧紧围绕全面建设社会主义现代化国家战略需求', '人才引领驱动重在巩固发展优势赢得竞争主动', '科技自立自强重在坚持独立自主开拓创新'}
2025-07-15 02:54:33.356 | INFO     | API.WorkTask:Xuan:1135 - ID:17328022636,尝试使用短语匹配，答案短语: ['教育优先发展重在夯实人力资源深度开发基础\n科技自立自强重在坚持独立自主开拓创新\n人才引领驱动重在巩固发展优势赢得竞争主动\n三者都紧紧围绕全面建设社会主义现代化国家战略需求']
2025-07-15 02:54:33.356 | ERROR    | API.WorkTask:Xuan:1243 - ID:17328022636,使用match_answer函数匹配失败: match_answer() missing 2 required positional arguments: 'qtp' and 'username'
2025-07-15 02:54:33.357 | WARNING  | API.WorkTask:Xuan:1247 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 02:54:33.357 | WARNING  | API.WorkTask:get_ai_answer_for_choice:1747 - ID:17328022636,AI回答无法匹配任何选项: 教育优先发展重在夯实人力资源深度开发基础
科技自立自强重在坚持独立自主开拓创新
人才引领驱动重在巩固发展优势赢得竞争主动
三者都紧紧围绕全面建设社会主义现代化国家战略需求
2025-07-15 02:54:33.358 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 19/22 个题目
2025-07-15 02:54:48.882 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 02:55:00.994 | INFO     | API.WorkTask:get_ai_answer_for_choice:1703 - ID:17328022636,AI返回的答案内容: 全面建成社会主义现代化强国的战略先导
实现高水平科技自立自强的重要支撑
促进全体人民共同富裕的有效途径
以中国式现代化全面推进中华民族伟大复兴的基础工程
2025-07-15 02:55:00.996 | INFO     | API.WorkTask:Xuan:968 - ID:17328022636,找到选项: {'A': 'A全面建成社会主义现代化强国的战略先导', 'B': 'B实现高水平科技自立自强的重要支撑', 'C': 'C促进全体人民共同富裕的有效途径', 'D': 'D以中国式现代化全面推进中华民族伟大复兴的基础工程'}
2025-07-15 02:55:00.996 | INFO     | API.WorkTask:Xuan:969 - ID:17328022636,题库答案: 全面建成社会主义现代化强国的战略先导
实现高水平科技自立自强的重要支撑
促进全体人民共同富裕的有效途径
以中国式现代化全面推进中华民族伟大复兴的基础工程
2025-07-15 02:55:00.997 | INFO     | API.WorkTask:Xuan:1066 - ID:17328022636,尝试使用相似度匹配
2025-07-15 02:55:00.998 | INFO     | API.WorkTask:Xuan:1086 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'实现高水平科技自立自强的重要支撑', '全面建成社会主义现代化强国的战略先导', '促进全体人民共同富裕的有效途径', '以中国式现代化全面推进中华民族伟大复兴的基础工程'}
2025-07-15 02:55:01.001 | INFO     | API.WorkTask:Xuan:1135 - ID:17328022636,尝试使用短语匹配，答案短语: ['全面建成社会主义现代化强国的战略先导\n实现高水平科技自立自强的重要支撑\n促进全体人民共同富裕的有效途径\n以中国式现代化全面推进中华民族伟大复兴的基础工程']
2025-07-15 02:55:01.001 | ERROR    | API.WorkTask:Xuan:1243 - ID:17328022636,使用match_answer函数匹配失败: match_answer() missing 2 required positional arguments: 'qtp' and 'username'
2025-07-15 02:55:01.003 | WARNING  | API.WorkTask:Xuan:1247 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 02:55:01.003 | WARNING  | API.WorkTask:get_ai_answer_for_choice:1747 - ID:17328022636,AI回答无法匹配任何选项: 全面建成社会主义现代化强国的战略先导
实现高水平科技自立自强的重要支撑
促进全体人民共同富裕的有效途径
以中国式现代化全面推进中华民族伟大复兴的基础工程
2025-07-15 02:55:01.004 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 20/22 个题目
2025-07-15 02:55:25.407 | INFO     | API.WorkTask:get_ai_answer_for_choice:1703 - ID:17328022636,AI返回的答案内容: 基础性
先导性
全局性
2025-07-15 02:55:25.410 | INFO     | API.WorkTask:Xuan:968 - ID:17328022636,找到选项: {'A': 'A基础性', 'B': 'B先导性', 'C': 'C决定性', 'D': 'D全局性'}
2025-07-15 02:55:25.411 | INFO     | API.WorkTask:Xuan:969 - ID:17328022636,题库答案: 基础性
先导性
全局性
2025-07-15 02:55:25.411 | INFO     | API.WorkTask:Xuan:1066 - ID:17328022636,尝试使用相似度匹配
2025-07-15 02:55:25.414 | INFO     | API.WorkTask:Xuan:1086 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'基础性', '全局性', '先导性'}
2025-07-15 02:55:25.420 | INFO     | API.WorkTask:Xuan:1135 - ID:17328022636,尝试使用短语匹配，答案短语: ['基础性\n先导性\n全局性']
2025-07-15 02:55:25.421 | ERROR    | API.WorkTask:Xuan:1243 - ID:17328022636,使用match_answer函数匹配失败: match_answer() missing 2 required positional arguments: 'qtp' and 'username'
2025-07-15 02:55:25.421 | WARNING  | API.WorkTask:Xuan:1247 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 02:55:25.423 | WARNING  | API.WorkTask:get_ai_answer_for_choice:1747 - ID:17328022636,AI回答无法匹配任何选项: 基础性
先导性
全局性
2025-07-15 02:55:25.424 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 21/22 个题目
2025-07-15 02:55:48.883 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 02:55:50.132 | INFO     | API.WorkTask:get_ai_answer_for_choice:1703 - ID:17328022636,AI返回的答案内容: 培养什么人
怎样培养人
为谁培养人
2025-07-15 02:55:50.134 | INFO     | API.WorkTask:Xuan:968 - ID:17328022636,找到选项: {'A': 'A为什么培养人', 'B': 'B培养什么人', 'C': 'C怎样培养人', 'D': 'D为谁培养人'}
2025-07-15 02:55:50.135 | INFO     | API.WorkTask:Xuan:969 - ID:17328022636,题库答案: 培养什么人
怎样培养人
为谁培养人
2025-07-15 02:55:50.135 | INFO     | API.WorkTask:Xuan:1066 - ID:17328022636,尝试使用相似度匹配
2025-07-15 02:55:50.138 | INFO     | API.WorkTask:Xuan:1086 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'怎样培养人', '为谁培养人', '培养什么人'}
2025-07-15 02:55:50.143 | INFO     | API.WorkTask:Xuan:1135 - ID:17328022636,尝试使用短语匹配，答案短语: ['培养什么人\n怎样培养人\n为谁培养人']
2025-07-15 02:55:50.144 | ERROR    | API.WorkTask:Xuan:1243 - ID:17328022636,使用match_answer函数匹配失败: match_answer() missing 2 required positional arguments: 'qtp' and 'username'
2025-07-15 02:55:50.145 | WARNING  | API.WorkTask:Xuan:1247 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 02:55:50.145 | WARNING  | API.WorkTask:get_ai_answer_for_choice:1747 - ID:17328022636,AI回答无法匹配任何选项: 培养什么人
怎样培养人
为谁培养人
2025-07-15 02:55:50.146 | INFO     | API.WorkTask:Html_Wkrk:782 - ID:17328022636,已处理 22/22 个题目
2025-07-15 02:55:50.147 | INFO     | API.WorkTask:Html_Wkrk:835 - ID:17328022636,所有题目处理完成，共 22/22 个
2025-07-15 02:55:50.147 | INFO     | API.WorkTask:PostDo:1309 - ID:17328022636,AI答题比例: 100.00%, AI答题数量: 22, 题库答题比例: 0.00%, 题库答题数量: 0, 总题目数量: 22
2025-07-15 02:55:50.147 | INFO     | API.WorkTask:PostDo:1334 - ID:17328022636,题库答题比例:0.00%, 使用保存模式
2025-07-15 02:55:50.691 | SUCCESS  | API.WorkTask:PostDo:1378 - ID:17328022636,保存答案成功: {"msg":"保存成功！","status":true}
2025-07-15 02:55:50.693 | INFO     | API.WorkTask:_update_progress_info:1466 - ID:17328022636,尝试导入data.Porgres模块
2025-07-15 02:55:50.693 | INFO     | API.WorkTask:_update_progress_info:1472 - ID:17328022636,成功导入data.Porgres.StartProces
2025-07-15 02:55:50.694 | INFO     | API.WorkTask:_update_progress_info:1494 - ID:17328022636,创建StartProces对象
2025-07-15 02:55:50.694 | INFO     | API.WorkTask:_update_progress_info:1507 - ID:17328022636,调用get_platform_9004_progress方法
2025-07-15 02:55:50.695 | INFO     | API.WorkTask:_update_progress_info:1519 - ID:17328022636,进度更新: 100%, 第十五章 第一二节 (已保存) | 题库答题比例:0% | AI答题比例:100%，使用保存模式 | 进度: 1/1 | 更新: 2025-07-15 02:55:50 | 题库答题比例:0% | AI答题比例:100%，使用保存模式
2025-07-15 02:55:50.697 | SUCCESS  | Config.UserSql:__init__:22 - 数据库连接池初始化成功
2025-07-15 02:55:50.697 | INFO     | API.WorkTask:_update_progress_info:1527 - ID:17328022636,更新数据库记录, oid: 300001
2025-07-15 02:55:50.733 | SUCCESS  | __main__:_fallback_to_traditional_homework:1611 - ID:17328022636,进度:100%,详情:第十五章 第一二节 | 题库答题比例:0% | AI答题比例:100%，使用保存模式 | 进度: 1/1 | 更新: 2025-07-15 02:55:50
2025-07-15 02:55:50.735 | SUCCESS  | __main__:_fallback_to_traditional_homework:1626 - ID:17328022636,传统方法作业任务 第十五章 第一二节 处理成功
2025-07-15 02:55:50.735 | INFO     | __main__:studentstudy:1050 - ID:17328022636,作业任务 作业任务_42670108 完成，等待 5 秒后处理下一任务
2025-07-15 02:55:55.745 | INFO     | __main__:unregister_thread:74 - 注销线程: OID=300001, 当前活跃线程数: 0
2025-07-15 02:56:18.723 | WARNING  | __main__:signal_handler:104 - 接收到终止信号，正在安全关闭...
2025-07-15 02:56:18.724 | INFO     | __main__:<module>:1945 - 程序退出
