---
description: 
globs: 
alwaysApply: false
---
# 学习通自动化系统 - 分层规则管理

## 项目架构分析

### 核心模块划分
- **入口文件**: 学习通跑单启动文件.py - 系统主入口，初始化服务和启动多线程任务
- **配置模块**: Config/UserSql.py - 数据库连接配置与SQL操作封装
- **API模块**: 
  - API/Session.py - 会话管理与验证码处理
  - API/TaskDo.py - 任务执行核心逻辑
  - API/VideoTask.py - 视频任务处理
  - API/WorkTask.py - 作业任务处理
- **数据处理模块**:
  - data/Exam.py - 考试相关功能
  - data/Porgres.py - 进度跟踪与状态更新

### 架构模式
- **多线程模式**: 系统使用Python原生threading模块实现多线程处理
- **单进程多线程**: 所有任务在同一进程内的多个线程中执行
- **共享数据库连接池**: 所有线程共享同一个数据库连接池实例
- **无状态任务处理**: 每个任务线程相对独立，通过数据库同步状态

### 平台类型(cid)
- **9000**: 标准学习通平台，完整功能支持(视频、章节测验、考试)
- **9001**: 简化版平台，无需考试功能，完成课程即标记为已完成
- **9002**: 基础平台，仅支持基本课程功能
- **9003**: 标准学习通平台，完整功能支持(视频、章节测验、考试)，夜间不休息
- **9004**: 标准学习通平台，支持(作业)

## 核心流程分析

### 初始化流程
1. **数据库初始化**: 创建MySQL连接池(最大15个连接)
2. **数据清理**: 重置所有非终态订单为"待处理"状态
3. **主循环启动**: 启动order_get函数的无限循环

### 订单处理流程
1. **订单获取**: 从数据库获取状态为"待处理"或"补刷中"的订单
2. **线程创建**: 为每个订单创建独立的处理线程(Run函数)
3. **账号验证**: 检查同一账号是否有其他正在执行的订单
4. **登录处理**: 根据账号类型(手机号或学校ID)执行登录
5. **课程匹配**: 获取用户课程列表并匹配目标课程
6. **章节处理**: 获取课程章节并依次处理每个章节
7. **任务点处理**: 处理章节内的各类任务点(视频、文档、作业等)
8. **状态更新**: 实时更新订单处理进度和状态到数据库

### 任务执行流程
1. **任务识别**: 根据attachment数据识别任务类型
2. **任务分发**: 根据任务类型调用相应的处理方法
3. **任务完成**: 提交任务完成状态并等待一定时间
4. **进度更新**: 更新任务完成进度到数据库

## 多进程适应性分析

### 当前架构的局限性
1. **GIL限制**: Python的全局解释器锁(GIL)限制了多线程在CPU密集型任务上的性能
2. **资源竞争**: 所有线程共享同一个数据库连接池，可能导致连接资源竞争
3. **错误传播**: 单进程模式下，一个线程的崩溃可能影响整个系统
4. **扩展性受限**: 单机多线程模式难以横向扩展到多台服务器

### 多进程架构改进方案
1. **进程池设计**: 使用multiprocessing模块替代threading，实现真正的并行处理
2. **分布式任务队列**: 引入Redis或RabbitMQ作为任务队列，实现任务的分布式处理
3. **独立数据库连接**: 每个进程维护自己的数据库连接池，避免资源竞争
4. **状态同步机制**: 使用Redis等分布式缓存同步各进程间的状态信息
5. **负载均衡**: 实现基于任务类型和系统负载的智能任务分配

### 推荐架构转型路径
1. **模块解耦**: 将订单获取、任务处理等功能解耦为独立模块
2. **消息队列引入**: 添加任务队列作为订单处理和任务执行的中间层
3. **进程池实现**: 替换当前的线程创建模式为进程池模式
4. **监控系统**: 添加系统监控和自动恢复机制，提高系统稳定性
5. **配置中心**: 引入集中式配置管理，便于动态调整系统参数

## 代码规范

### 错误处理
- 所有网络请求必须使用try-except包装
- 捕获异常后记录详细日志并优雅降级
- 数据库操作失败后等待5秒重试
- 选择题答案处理必须处理None返回值情况
- 答案匹配失败时使用默认答案('A')而不是抛出异常

### 日志规范
- 使用loguru进行统一日志管理
- 成功操作使用logger.success
- 错误使用logger.error并包含详细错误信息
- 调试信息使用logger.debug或logger.info
- 答案处理错误需记录用户ID和具体错误信息

## 安全规范

### 账号安全
- 密码使用AES加密后传输
- 验证码处理使用OCR自动识别
- 敏感信息不在日志中明文显示

### 网络安全
- 禁用代理服务器，使用直连方式
- 模拟正常用户行为，避免触发反爬机制
- 请求头中包含合理的User-Agent信息

## 数据库规范

### 连接管理
- 使用连接池管理数据库连接(maxconnections=15)
- 所有SQL操作自动提交(autocommit=True)
- 使用DictCursor返回字典格式结果

### 查询优化
- 使用ORDER BY替代GROUP BY避免MySQL错误
- 批量查询使用get_order，单条查询使用get_order_dan
- 更新操作使用update_order并验证结果

## 业务规则

### 订单处理
- 只处理cid为9000、9001、9002、9003的订单
- 状态为"待处理"或"补刷中"的订单才会被执行
- 同一账号同时只能执行一个订单，其他订单等待

### 夜间休息
- 23:10至次日7:30期间暂停所有任务执行(cid=9003除外)
- 休息期间每15分钟检查一次时间
- 休息期间订单状态标记为"停止中"

### 答案处理策略
- **多级匹配**: 使用不同的匹配阈值(0.95→0.9→0.7→0.65)逐步降低匹配精度
- **多源查询**: 题库匹配失败后尝试从其他题库获取答案
- **默认答案**: 无法匹配时使用默认答案('A'或'无答案')
- **异常处理**: 答案处理过程中的异常必须捕获并记录，不能中断整体流程
- **空值处理**: 答案为None时必须提供默认值，避免解包错误

## 速度控制规则

### 答题速度
- **作业答题**: 每道题处理后等待0.5秒，提交后等待0.5秒
- **考试答题**: 非最后一题等待0.05秒，最后一题等待0.5秒，考试完成后等待10-15秒
- **验证码处理**: 验证码识别失败后等待1-2秒重试

### 任务间隔
- 每个任务点处理后等待0.5秒
- 每章节完成后随机等待60-90秒(1-1.5分钟)
- 视频任务完成后随机等待2-5秒

### 线程控制
- 每个新订单线程创建后等待5秒
- 每次获取订单列表后等待30秒
- 错误重试等待时间为5-20秒

### 网络请求
- 数据库查询失败后等待5秒重试
- 验证码识别失败后等待5秒重试
- 网络请求超时设置为5秒

## 故障排查指南

### 常见错误
- **TypeError: cannot unpack non-iterable NoneType object**: 答案处理函数返回None但尝试解包
- **连接池初始化失败**: 检查数据库连接参数和网络连接
- **提交失败，参数异常**: 表单参数不完整或格式错误
- **章节老师未设置内容**: 课程章节没有具体任务点，可以跳过

### 解决方案
- 答案处理错误: 确保所有返回值都有默认处理，避免None值导致解包错误
- 数据库连接问题: 检查数据库服务是否正常运行，参数是否正确
- 网络请求失败: 增加重试次数和等待时间，检查网络连接
- 验证码识别失败: 尝试使用备用识别方法或人工干预

