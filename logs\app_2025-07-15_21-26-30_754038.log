2025-07-15 21:26:30.755 | INFO     | __main__:<module>:1888 - 学习通自动化系统启动...
2025-07-15 21:26:30.755 | SUCCESS  | Config.UserSql:__init__:22 - 数据库连接池初始化成功
2025-07-15 21:26:30.755 | SUCCESS  | __main__:<module>:1898 - 数据库连接池初始化成功
2025-07-15 21:26:30.761 | SUCCESS  | __main__:<module>:1919 - 数据状态重置完成
2025-07-15 21:26:30.761 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 21:26:30.761 | INFO     | __main__:<module>:1935 - 开始处理订单...
2025-07-15 21:26:30.761 | INFO     | __main__:order_get:1691 - 订单处理线程启动，最大线程数: 100
2025-07-15 21:26:30.763 | INFO     | __main__:order_get:1716 - 获取到 1 个待处理订单，当前活跃线程数: 0
2025-07-15 21:26:30.764 | INFO     | __main__:Run:1775 - 开始处理订单: OID=300001, 用户=17328022636, 课程ID=246628486|107051161|352747128|42670108|6a76f288656266a67fb6a4820a3e7864|5ac17897c0eea6971f688e43b15b8f51
2025-07-15 21:26:30.764 | INFO     | __main__:register_thread:67 - 注册线程: OID=300001, 当前活跃线程数: 1
2025-07-15 21:26:31.289 | SUCCESS  | __main__:Run:1819 - ID:17328022636,登录成功
2025-07-15 21:26:31.291 | INFO     | __main__:kclist:225 - ID:17328022636,平台ID 9004，使用特殊处理方式获取作业信息
2025-07-15 21:26:31.291 | SUCCESS  | __main__:kclist:242 - ID:17328022636,成功构造作业课程信息: {'kcname': '作业任务', 'courseid': '246628486|107051161|352747128|42670108|6a76f288656266a67fb6a4820a3e7864|5ac17897c0eea6971f688e43b15b8f51', 'clazzid': '246628486|107051161|352747128|42670108|6a76f288656266a67fb6a4820a3e7864|5ac17897c0eea6971f688e43b15b8f51', 'cpi': ''}
2025-07-15 21:26:31.292 | SUCCESS  | __main__:Run:1828 - ID:17328022636,课程信息匹配成功
2025-07-15 21:26:31.294 | SUCCESS  | __main__:studentstudy:935 - ID:17328022636,进度:100%,详情:作业任务_42670108 | 进行中 | 进度: 1/1 | 更新: 2025-07-15 21:26:31
2025-07-15 21:26:32.396 | INFO     | __main__:_fallback_to_traditional_homework:1496 - ID:17328022636,获取到真实作业标题: 第十五章 第一二节
2025-07-15 21:26:32.998 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1394 - ID:17328022636,提取到基本表单字段: courseId=246628486, classId=107051161, workId=42670108
2025-07-15 21:26:33.001 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1405 - ID:17328022636,找到 22 个题目项
2025-07-15 21:26:33.005 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1535 - ID:17328022636,成功提取到 22 个问题
2025-07-15 21:26:33.005 | ERROR    | API.HomeworkAI:process_homework:1025 - ID:17328022636,未生成任何答案
2025-07-15 21:26:33.681 | INFO     | API.WorkTask:Html_Wkrk:185 - ID:17328022636,尝试解析作业页面参数
2025-07-15 21:26:45.572 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 教育
2025-07-15 21:26:45.573 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A教育', 'B': 'B科技', 'C': 'C人才', 'D': 'D创新'}
2025-07-15 21:26:45.574 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 教育
2025-07-15 21:26:45.574 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: A
2025-07-15 21:26:45.574 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 1/22 个题目
2025-07-15 21:26:56.724 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 科技
2025-07-15 21:26:56.725 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A教育', 'B': 'B科技', 'C': 'C人才', 'D': 'D创新'}
2025-07-15 21:26:56.725 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 科技
2025-07-15 21:26:56.726 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: B
2025-07-15 21:26:56.726 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 2/22 个题目
2025-07-15 21:27:09.024 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 教育
2025-07-15 21:27:09.026 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A教育', 'B': 'B科技', 'C': 'C人才', 'D': 'D创新'}
2025-07-15 21:27:09.026 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 教育
2025-07-15 21:27:09.027 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: A
2025-07-15 21:27:09.027 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 3/22 个题目
2025-07-15 21:27:20.789 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 人才
2025-07-15 21:27:20.791 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A科技', 'B': 'B人才', 'C': 'C创新', 'D': 'D教育'}
2025-07-15 21:27:20.792 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 人才
2025-07-15 21:27:20.792 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: B
2025-07-15 21:27:20.792 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 4/22 个题目
2025-07-15 21:27:30.762 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 21:27:31.056 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A科技', 'B': 'B人才', 'C': 'C创新', 'D': 'D教育'}
2025-07-15 21:27:31.056 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 人才
2025-07-15 21:27:31.056 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: B
2025-07-15 21:27:31.057 | SUCCESS  | API.WorkTask:Html_Wkrk:486 - ID:17328022636,主题库答案匹配成功: B
2025-07-15 21:27:31.057 | INFO     | API.WorkTask:Html_Wkrk:543 - ID:17328022636,已处理 5/22 个题目
2025-07-15 21:27:43.401 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 创新
2025-07-15 21:27:43.402 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A科技', 'B': 'B人才', 'C': 'C创新', 'D': 'D教育'}
2025-07-15 21:27:43.403 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 创新
2025-07-15 21:27:43.403 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: C
2025-07-15 21:27:43.403 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 6/22 个题目
2025-07-15 21:27:55.681 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 教育优先发展
2025-07-15 21:27:55.683 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A创新驱动发展', 'B': 'B科技自立自强', 'C': 'C人才引领驱动', 'D': 'D教育优先发展'}
2025-07-15 21:27:55.683 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 教育优先发展
2025-07-15 21:27:55.683 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: D
2025-07-15 21:27:55.684 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 7/22 个题目
2025-07-15 21:28:05.473 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A创新驱动发展', 'B': 'B科技自立自强', 'C': 'C人才引领驱动', 'D': 'D教育优先发展'}
2025-07-15 21:28:05.474 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 科技自立自强
2025-07-15 21:28:05.474 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: B
2025-07-15 21:28:05.474 | SUCCESS  | API.WorkTask:Html_Wkrk:486 - ID:17328022636,主题库答案匹配成功: B
2025-07-15 21:28:05.474 | INFO     | API.WorkTask:Html_Wkrk:543 - ID:17328022636,已处理 8/22 个题目
2025-07-15 21:28:15.522 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A创新驱动发展', 'B': 'B科技自立自强', 'C': 'C人才引领驱动', 'D': 'D教育优先发展'}
2025-07-15 21:28:15.523 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 人才引领驱动
2025-07-15 21:28:15.523 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: C
2025-07-15 21:28:15.523 | SUCCESS  | API.WorkTask:Html_Wkrk:486 - ID:17328022636,主题库答案匹配成功: C
2025-07-15 21:28:15.524 | INFO     | API.WorkTask:Html_Wkrk:543 - ID:17328022636,已处理 9/22 个题目
2025-07-15 21:28:18.287 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A坚持党对教育工作的全面领导', 'B': 'B坚持社会主义教育方向', 'C': 'C坚持中国特色社会主义教育道路', 'D': 'D坚持立德树人根本任务'}
2025-07-15 21:28:18.289 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 加强党对教育工作的全面领导
2025-07-15 21:28:18.289 | INFO     | API.WorkTask:Xuan:1070 - ID:17328022636,尝试使用相似度匹配
2025-07-15 21:28:18.291 | INFO     | API.WorkTask:Xuan:1090 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'加强党对教育工作的全面领导'}
2025-07-15 21:28:18.295 | INFO     | API.WorkTask:Xuan:1139 - ID:17328022636,尝试使用短语匹配，答案短语: ['加强党对教育工作的全面领导']
2025-07-15 21:28:18.295 | ERROR    | API.WorkTask:Xuan:1247 - ID:17328022636,使用match_answer函数匹配失败: match_answer() missing 2 required positional arguments: 'qtp' and 'username'
2025-07-15 21:28:18.295 | WARNING  | API.WorkTask:Xuan:1251 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 21:28:18.296 | WARNING  | API.WorkTask:Html_Wkrk:548 - ID:17328022636,主题库答案处理失败，尝试备用题库
2025-07-15 21:28:18.517 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A坚持党对教育工作的全面领导', 'B': 'B坚持社会主义教育方向', 'C': 'C坚持中国特色社会主义教育道路', 'D': 'D坚持立德树人根本任务'}
2025-07-15 21:28:18.517 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 坚持党对教育工作的全面领导
2025-07-15 21:28:18.518 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: A
2025-07-15 21:28:18.518 | SUCCESS  | API.WorkTask:Html_Wkrk:581 - ID:17328022636,备用题库答案匹配成功: A
2025-07-15 21:28:18.518 | INFO     | API.WorkTask:Html_Wkrk:635 - ID:17328022636,已处理 10/22 个题目
2025-07-15 21:28:30.763 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 21:28:33.974 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 党的教育方针
2025-07-15 21:28:33.976 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A科教兴国战略', 'B': 'B人才强国战略', 'C': 'C社会主义教育方针', 'D': 'D党的教育方针'}
2025-07-15 21:28:33.976 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 党的教育方针
2025-07-15 21:28:33.977 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: D
2025-07-15 21:28:33.977 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 11/22 个题目
2025-07-15 21:28:46.265 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 立德树人
2025-07-15 21:28:46.266 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A发展中国特色社会主义教育', 'B': 'B立德树人', 'C': 'C巩固和发展社会主义制度', 'D': 'D教育公平'}
2025-07-15 21:28:46.267 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 立德树人
2025-07-15 21:28:46.267 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: B
2025-07-15 21:28:46.267 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 12/22 个题目
2025-07-15 21:28:56.080 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A基础文化理论课', 'B': 'B劳动技术教育课', 'C': 'C思想政治理论课', 'D': 'D社会公德教育课'}
2025-07-15 21:28:56.081 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 思想政治理论课
2025-07-15 21:28:56.081 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: C
2025-07-15 21:28:56.082 | SUCCESS  | API.WorkTask:Html_Wkrk:486 - ID:17328022636,主题库答案匹配成功: C
2025-07-15 21:28:56.082 | INFO     | API.WorkTask:Html_Wkrk:543 - ID:17328022636,已处理 13/22 个题目
2025-07-15 21:29:08.057 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 坚持中国特色社会主义教育道路
2025-07-15 21:29:08.059 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A坚持党对教育工作的全面领导', 'B': 'B坚持中国特色社会主义教育道路', 'C': 'C培养理想信念坚定的社会主义建设者和接班人', 'D': 'D培养德才兼备的社会公民'}
2025-07-15 21:29:08.060 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 坚持中国特色社会主义教育道路
2025-07-15 21:29:08.060 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: B
2025-07-15 21:29:08.060 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 14/22 个题目
2025-07-15 21:29:18.287 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A高质量发展', 'B': 'B立德树人', 'C': 'C教育优先发展', 'D': 'D人民满意'}
2025-07-15 21:29:18.287 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 高质量发展
2025-07-15 21:29:18.288 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: A
2025-07-15 21:29:18.288 | SUCCESS  | API.WorkTask:Html_Wkrk:486 - ID:17328022636,主题库答案匹配成功: A
2025-07-15 21:29:18.288 | INFO     | API.WorkTask:Html_Wkrk:543 - ID:17328022636,已处理 15/22 个题目
2025-07-15 21:29:28.495 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A坚定不移推进教育公平', 'B': 'B构建全社会共同育人格局', 'C': 'C加强教育基础设施建设', 'D': 'D加强教师队伍建设'}
2025-07-15 21:29:28.495 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 培养造就新时代高水平教师队伍
2025-07-15 21:29:28.496 | INFO     | API.WorkTask:Xuan:1070 - ID:17328022636,尝试使用相似度匹配
2025-07-15 21:29:28.497 | INFO     | API.WorkTask:Xuan:1090 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'培养造就新时代高水平教师队伍'}
2025-07-15 21:29:28.499 | INFO     | API.WorkTask:Xuan:1139 - ID:17328022636,尝试使用短语匹配，答案短语: ['培养造就新时代高水平教师队伍']
2025-07-15 21:29:28.499 | ERROR    | API.WorkTask:Xuan:1247 - ID:17328022636,使用match_answer函数匹配失败: match_answer() missing 2 required positional arguments: 'qtp' and 'username'
2025-07-15 21:29:28.500 | WARNING  | API.WorkTask:Xuan:1251 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 21:29:28.501 | WARNING  | API.WorkTask:Html_Wkrk:548 - ID:17328022636,主题库答案处理失败，尝试备用题库
2025-07-15 21:29:28.680 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A坚定不移推进教育公平', 'B': 'B构建全社会共同育人格局', 'C': 'C加强教育基础设施建设', 'D': 'D加强教师队伍建设'}
2025-07-15 21:29:28.681 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 培养造就新时代高水平教师队伍
2025-07-15 21:29:28.681 | INFO     | API.WorkTask:Xuan:1070 - ID:17328022636,尝试使用相似度匹配
2025-07-15 21:29:28.682 | INFO     | API.WorkTask:Xuan:1090 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'培养造就新时代高水平教师队伍'}
2025-07-15 21:29:28.684 | INFO     | API.WorkTask:Xuan:1139 - ID:17328022636,尝试使用短语匹配，答案短语: ['培养造就新时代高水平教师队伍']
2025-07-15 21:29:28.685 | ERROR    | API.WorkTask:Xuan:1247 - ID:17328022636,使用match_answer函数匹配失败: match_answer() missing 2 required positional arguments: 'qtp' and 'username'
2025-07-15 21:29:28.685 | WARNING  | API.WorkTask:Xuan:1251 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 21:29:28.685 | WARNING  | API.WorkTask:Html_Wkrk:640 - ID:17328022636,备用题库答案处理失败，尝试AI答题
2025-07-15 21:29:30.632 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 加强教师队伍建设
2025-07-15 21:29:30.633 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A坚定不移推进教育公平', 'B': 'B构建全社会共同育人格局', 'C': 'C加强教育基础设施建设', 'D': 'D加强教师队伍建设'}
2025-07-15 21:29:30.633 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 加强教师队伍建设
2025-07-15 21:29:30.633 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: D
2025-07-15 21:29:30.634 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 16/22 个题目
2025-07-15 21:29:30.674 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A科技', 'B': 'B人才', 'C': 'C创新', 'D': 'D教育'}
2025-07-15 21:29:30.674 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 教育###科技###人才
2025-07-15 21:29:30.675 | INFO     | API.WorkTask:Xuan:1070 - ID:17328022636,尝试使用相似度匹配
2025-07-15 21:29:30.676 | INFO     | API.WorkTask:Xuan:1090 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'人才', '科技', '教育'}
2025-07-15 21:29:30.678 | INFO     | API.WorkTask:Xuan:1139 - ID:17328022636,尝试使用短语匹配，答案短语: ['教育###科技###人才']
2025-07-15 21:29:30.678 | ERROR    | API.WorkTask:Xuan:1247 - ID:17328022636,使用match_answer函数匹配失败: match_answer() missing 2 required positional arguments: 'qtp' and 'username'
2025-07-15 21:29:30.678 | WARNING  | API.WorkTask:Xuan:1251 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 21:29:30.678 | WARNING  | API.WorkTask:Html_Wkrk:548 - ID:17328022636,主题库答案处理失败，尝试备用题库
2025-07-15 21:29:30.765 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 21:29:30.890 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A科技', 'B': 'B人才', 'C': 'C创新', 'D': 'D教育'}
2025-07-15 21:29:30.890 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 教育、科技、人才
2025-07-15 21:29:30.890 | INFO     | API.WorkTask:Xuan:1070 - ID:17328022636,尝试使用相似度匹配
2025-07-15 21:29:30.891 | INFO     | API.WorkTask:Xuan:1090 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'人才', '科技', '教育'}
2025-07-15 21:29:30.893 | INFO     | API.WorkTask:Xuan:1139 - ID:17328022636,尝试使用短语匹配，答案短语: ['教育', '科技', '人才']
2025-07-15 21:29:30.894 | ERROR    | API.WorkTask:Xuan:1247 - ID:17328022636,使用match_answer函数匹配失败: match_answer() missing 2 required positional arguments: 'qtp' and 'username'
2025-07-15 21:29:30.894 | WARNING  | API.WorkTask:Xuan:1251 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 21:29:30.894 | WARNING  | API.WorkTask:Html_Wkrk:640 - ID:17328022636,备用题库答案处理失败，尝试AI答题
2025-07-15 21:29:32.695 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 教育
科技
人才
2025-07-15 21:29:32.697 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A科技', 'B': 'B人才', 'C': 'C创新', 'D': 'D教育'}
2025-07-15 21:29:32.697 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 教育
科技
人才
2025-07-15 21:29:32.698 | INFO     | API.WorkTask:Xuan:1070 - ID:17328022636,尝试使用相似度匹配
2025-07-15 21:29:32.699 | INFO     | API.WorkTask:Xuan:1090 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'人才', '科技', '教育'}
2025-07-15 21:29:32.702 | INFO     | API.WorkTask:Xuan:1139 - ID:17328022636,尝试使用短语匹配，答案短语: ['教育\n科技\n人才']
2025-07-15 21:29:32.703 | ERROR    | API.WorkTask:Xuan:1247 - ID:17328022636,使用match_answer函数匹配失败: match_answer() missing 2 required positional arguments: 'qtp' and 'username'
2025-07-15 21:29:32.703 | WARNING  | API.WorkTask:Xuan:1251 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 21:29:32.703 | WARNING  | API.WorkTask:get_ai_answer_for_choice:1811 - ID:17328022636,AI回答无法匹配任何选项: 教育
科技
人才
2025-07-15 21:29:32.704 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 17/22 个题目
2025-07-15 21:29:42.216 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A我国教育现代化发展总体水平跨入世界中上国家行列', 'B': 'B我国进入创新型强国行首', 'C': 'C我国已建成世界上规模最大的教育体系', 'D': 'D我国已经发展成为全球规模最宏大的人才资源大国'}
2025-07-15 21:29:42.216 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 道是老子思想的核心###在老子看来，人类社会不是进化的，而是不断倒退的###无为而治是老子的政治主张
2025-07-15 21:29:42.216 | INFO     | API.WorkTask:Xuan:1070 - ID:17328022636,尝试使用相似度匹配
2025-07-15 21:29:42.218 | INFO     | API.WorkTask:Xuan:1090 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'人类社会不是进化的', '无为而治是老子的政治主张', '道是老子思想的核心', '在老子看来', '而是不断倒退的'}
2025-07-15 21:29:42.219 | INFO     | API.WorkTask:Xuan:1139 - ID:17328022636,尝试使用短语匹配，答案短语: ['道是老子思想的核心###在老子看来', '人类社会不是进化的', '而是不断倒退的###无为而治是老子的政治主张']
2025-07-15 21:29:42.220 | ERROR    | API.WorkTask:Xuan:1247 - ID:17328022636,使用match_answer函数匹配失败: match_answer() missing 2 required positional arguments: 'qtp' and 'username'
2025-07-15 21:29:42.220 | WARNING  | API.WorkTask:Xuan:1251 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 21:29:42.220 | WARNING  | API.WorkTask:Html_Wkrk:548 - ID:17328022636,主题库答案处理失败，尝试备用题库
2025-07-15 21:29:44.921 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 我国教育现代化发展总体水平跨入世界中上国家行列
我国已建成世界上规模最大的教育体系
我国已经发展成为全球规模最宏大的人才资源大国
2025-07-15 21:29:44.923 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A我国教育现代化发展总体水平跨入世界中上国家行列', 'B': 'B我国进入创新型强国行首', 'C': 'C我国已建成世界上规模最大的教育体系', 'D': 'D我国已经发展成为全球规模最宏大的人才资源大国'}
2025-07-15 21:29:44.924 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 我国教育现代化发展总体水平跨入世界中上国家行列
我国已建成世界上规模最大的教育体系
我国已经发展成为全球规模最宏大的人才资源大国
2025-07-15 21:29:44.924 | INFO     | API.WorkTask:Xuan:1070 - ID:17328022636,尝试使用相似度匹配
2025-07-15 21:29:44.925 | INFO     | API.WorkTask:Xuan:1090 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'我国已经发展成为全球规模最宏大的人才资源大国', '我国已建成世界上规模最大的教育体系', '我国教育现代化发展总体水平跨入世界中上国家行列'}
2025-07-15 21:29:44.928 | INFO     | API.WorkTask:Xuan:1139 - ID:17328022636,尝试使用短语匹配，答案短语: ['我国教育现代化发展总体水平跨入世界中上国家行列\n我国已建成世界上规模最大的教育体系\n我国已经发展成为全球规模最宏大的人才资源大国']
2025-07-15 21:29:44.929 | ERROR    | API.WorkTask:Xuan:1247 - ID:17328022636,使用match_answer函数匹配失败: match_answer() missing 2 required positional arguments: 'qtp' and 'username'
2025-07-15 21:29:44.929 | WARNING  | API.WorkTask:Xuan:1251 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 21:29:44.930 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 18/22 个题目
2025-07-15 21:29:57.909 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 教育优先发展重在夯实人力资源深度开发基础
科技自立自强重在坚持独立自主开拓创新
人才引领驱动重在巩固发展优势赢得竞争主动
三者都紧紧围绕全面建设社会主义现代化国家战略需求
2025-07-15 21:29:57.910 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A教育优先发展重在夯实人力资源深度开发基础', 'B': 'B科技自立自强重在坚持独立自主开拓创新', 'C': 'C人才引领驱动重在巩固发展优势赢得竞争主动', 'D': 'D三者都紧紧围绕全面建设社会主义现代化国家战略需求'}
2025-07-15 21:29:57.911 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 教育优先发展重在夯实人力资源深度开发基础
科技自立自强重在坚持独立自主开拓创新
人才引领驱动重在巩固发展优势赢得竞争主动
三者都紧紧围绕全面建设社会主义现代化国家战略需求
2025-07-15 21:29:57.911 | INFO     | API.WorkTask:Xuan:1070 - ID:17328022636,尝试使用相似度匹配
2025-07-15 21:29:57.913 | INFO     | API.WorkTask:Xuan:1090 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'三者都紧紧围绕全面建设社会主义现代化国家战略需求', '科技自立自强重在坚持独立自主开拓创新', '人才引领驱动重在巩固发展优势赢得竞争主动', '教育优先发展重在夯实人力资源深度开发基础'}
2025-07-15 21:29:57.916 | INFO     | API.WorkTask:Xuan:1139 - ID:17328022636,尝试使用短语匹配，答案短语: ['教育优先发展重在夯实人力资源深度开发基础\n科技自立自强重在坚持独立自主开拓创新\n人才引领驱动重在巩固发展优势赢得竞争主动\n三者都紧紧围绕全面建设社会主义现代化国家战略需求']
2025-07-15 21:29:57.917 | ERROR    | API.WorkTask:Xuan:1247 - ID:17328022636,使用match_answer函数匹配失败: match_answer() missing 2 required positional arguments: 'qtp' and 'username'
2025-07-15 21:29:57.917 | WARNING  | API.WorkTask:Xuan:1251 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 21:29:57.917 | WARNING  | API.WorkTask:get_ai_answer_for_choice:1811 - ID:17328022636,AI回答无法匹配任何选项: 教育优先发展重在夯实人力资源深度开发基础
科技自立自强重在坚持独立自主开拓创新
人才引领驱动重在巩固发展优势赢得竞争主动
三者都紧紧围绕全面建设社会主义现代化国家战略需求
2025-07-15 21:29:57.918 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 19/22 个题目
2025-07-15 21:30:09.776 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 全面建成社会主义现代化强国的战略先导
实现高水平科技自立自强的重要支撑
促进全体人民共同富裕的有效途径
以中国式现代化全面推进中华民族伟大复兴的基础工程
2025-07-15 21:30:09.778 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A全面建成社会主义现代化强国的战略先导', 'B': 'B实现高水平科技自立自强的重要支撑', 'C': 'C促进全体人民共同富裕的有效途径', 'D': 'D以中国式现代化全面推进中华民族伟大复兴的基础工程'}
2025-07-15 21:30:09.779 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 全面建成社会主义现代化强国的战略先导
实现高水平科技自立自强的重要支撑
促进全体人民共同富裕的有效途径
以中国式现代化全面推进中华民族伟大复兴的基础工程
2025-07-15 21:30:09.779 | INFO     | API.WorkTask:Xuan:1070 - ID:17328022636,尝试使用相似度匹配
2025-07-15 21:30:09.781 | INFO     | API.WorkTask:Xuan:1090 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'以中国式现代化全面推进中华民族伟大复兴的基础工程', '实现高水平科技自立自强的重要支撑', '促进全体人民共同富裕的有效途径', '全面建成社会主义现代化强国的战略先导'}
2025-07-15 21:30:09.783 | INFO     | API.WorkTask:Xuan:1139 - ID:17328022636,尝试使用短语匹配，答案短语: ['全面建成社会主义现代化强国的战略先导\n实现高水平科技自立自强的重要支撑\n促进全体人民共同富裕的有效途径\n以中国式现代化全面推进中华民族伟大复兴的基础工程']
2025-07-15 21:30:09.783 | ERROR    | API.WorkTask:Xuan:1247 - ID:17328022636,使用match_answer函数匹配失败: match_answer() missing 2 required positional arguments: 'qtp' and 'username'
2025-07-15 21:30:09.783 | WARNING  | API.WorkTask:Xuan:1251 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 21:30:09.784 | WARNING  | API.WorkTask:get_ai_answer_for_choice:1811 - ID:17328022636,AI回答无法匹配任何选项: 全面建成社会主义现代化强国的战略先导
实现高水平科技自立自强的重要支撑
促进全体人民共同富裕的有效途径
以中国式现代化全面推进中华民族伟大复兴的基础工程
2025-07-15 21:30:09.784 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 20/22 个题目
2025-07-15 21:30:22.032 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 基础性
先导性
全局性
2025-07-15 21:30:22.034 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A基础性', 'B': 'B先导性', 'C': 'C决定性', 'D': 'D全局性'}
2025-07-15 21:30:22.034 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 基础性
先导性
全局性
2025-07-15 21:30:22.035 | INFO     | API.WorkTask:Xuan:1070 - ID:17328022636,尝试使用相似度匹配
2025-07-15 21:30:22.036 | INFO     | API.WorkTask:Xuan:1090 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'基础性', '先导性', '全局性'}
2025-07-15 21:30:22.039 | INFO     | API.WorkTask:Xuan:1139 - ID:17328022636,尝试使用短语匹配，答案短语: ['基础性\n先导性\n全局性']
2025-07-15 21:30:22.039 | ERROR    | API.WorkTask:Xuan:1247 - ID:17328022636,使用match_answer函数匹配失败: match_answer() missing 2 required positional arguments: 'qtp' and 'username'
2025-07-15 21:30:22.039 | WARNING  | API.WorkTask:Xuan:1251 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 21:30:22.040 | WARNING  | API.WorkTask:get_ai_answer_for_choice:1811 - ID:17328022636,AI回答无法匹配任何选项: 基础性
先导性
全局性
2025-07-15 21:30:22.040 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 21/22 个题目
2025-07-15 21:30:30.766 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 21:30:34.381 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 培养什么人
怎样培养人
为谁培养人
2025-07-15 21:30:34.382 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A为什么培养人', 'B': 'B培养什么人', 'C': 'C怎样培养人', 'D': 'D为谁培养人'}
2025-07-15 21:30:34.383 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 培养什么人
怎样培养人
为谁培养人
2025-07-15 21:30:34.383 | INFO     | API.WorkTask:Xuan:1070 - ID:17328022636,尝试使用相似度匹配
2025-07-15 21:30:34.384 | INFO     | API.WorkTask:Xuan:1090 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'为谁培养人', '怎样培养人', '培养什么人'}
2025-07-15 21:30:34.388 | INFO     | API.WorkTask:Xuan:1139 - ID:17328022636,尝试使用短语匹配，答案短语: ['培养什么人\n怎样培养人\n为谁培养人']
2025-07-15 21:30:34.388 | ERROR    | API.WorkTask:Xuan:1247 - ID:17328022636,使用match_answer函数匹配失败: match_answer() missing 2 required positional arguments: 'qtp' and 'username'
2025-07-15 21:30:34.388 | WARNING  | API.WorkTask:Xuan:1251 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 21:30:34.389 | WARNING  | API.WorkTask:get_ai_answer_for_choice:1811 - ID:17328022636,AI回答无法匹配任何选项: 培养什么人
怎样培养人
为谁培养人
2025-07-15 21:30:34.389 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 22/22 个题目
2025-07-15 21:30:34.390 | INFO     | API.WorkTask:Html_Wkrk:839 - ID:17328022636,所有题目处理完成，共 22/22 个
2025-07-15 21:30:34.390 | INFO     | API.WorkTask:PostDo:1313 - ID:17328022636,AI答题比例: 72.73%, AI答题数量: 16, 题库答题比例: 27.27%, 题库答题数量: 6, 总题目数量: 22
2025-07-15 21:30:34.390 | INFO     | API.WorkTask:PostDo:1338 - ID:17328022636,题库答题比例:27.27%, 使用保存模式
2025-07-15 21:30:34.862 | SUCCESS  | API.WorkTask:PostDo:1382 - ID:17328022636,保存答案成功: {"msg":"保存成功！","status":true}
2025-07-15 21:30:34.864 | INFO     | API.WorkTask:_update_progress_info:1470 - ID:17328022636,尝试导入data.Porgres模块
2025-07-15 21:30:34.865 | INFO     | API.WorkTask:_update_progress_info:1476 - ID:17328022636,成功导入data.Porgres.StartProces
2025-07-15 21:30:34.865 | INFO     | API.WorkTask:_update_progress_info:1498 - ID:17328022636,创建StartProces对象
2025-07-15 21:30:34.865 | INFO     | API.WorkTask:_update_progress_info:1511 - ID:17328022636,调用get_platform_9004_progress方法
2025-07-15 21:30:34.866 | INFO     | API.WorkTask:_update_progress_info:1523 - ID:17328022636,进度更新: 100%, 第十五章 第一二节 (已保存) | 题库答题比例:27% | AI答题比例:73%，使用保存模式 | 进度: 1/1 | 更新: 2025-07-15 21:30:34 | 题库答题比例:27% | AI答题比例:73%，使用保存模式
2025-07-15 21:30:34.866 | SUCCESS  | Config.UserSql:__init__:22 - 数据库连接池初始化成功
2025-07-15 21:30:34.867 | INFO     | API.WorkTask:_update_progress_info:1531 - ID:17328022636,更新数据库记录, oid: 300001
2025-07-15 21:30:34.882 | SUCCESS  | __main__:_fallback_to_traditional_homework:1611 - ID:17328022636,进度:100%,详情:第十五章 第一二节 | 题库答题比例:27% | AI答题比例:73%，使用保存模式 | 进度: 1/1 | 更新: 2025-07-15 21:30:34
2025-07-15 21:30:34.883 | SUCCESS  | __main__:_fallback_to_traditional_homework:1626 - ID:17328022636,传统方法作业任务 第十五章 第一二节 处理成功
2025-07-15 21:30:34.883 | INFO     | __main__:studentstudy:1050 - ID:17328022636,作业任务 作业任务_42670108 完成，等待 8 秒后处理下一任务
2025-07-15 21:30:42.893 | INFO     | __main__:unregister_thread:74 - 注销线程: OID=300001, 当前活跃线程数: 0
2025-07-15 21:30:46.377 | WARNING  | __main__:signal_handler:104 - 接收到终止信号，正在安全关闭...
2025-07-15 21:30:46.377 | INFO     | __main__:<module>:1945 - 程序退出
