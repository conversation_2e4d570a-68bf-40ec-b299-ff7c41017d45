2025-07-15 21:42:48.241 | INFO     | __main__:<module>:1888 - 学习通自动化系统启动...
2025-07-15 21:42:48.241 | SUCCESS  | Config.UserSql:__init__:22 - 数据库连接池初始化成功
2025-07-15 21:42:48.241 | SUCCESS  | __main__:<module>:1898 - 数据库连接池初始化成功
2025-07-15 21:42:48.247 | SUCCESS  | __main__:<module>:1919 - 数据状态重置完成
2025-07-15 21:42:48.247 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 21:42:48.248 | INFO     | __main__:<module>:1935 - 开始处理订单...
2025-07-15 21:42:48.248 | INFO     | __main__:order_get:1691 - 订单处理线程启动，最大线程数: 100
2025-07-15 21:42:48.250 | INFO     | __main__:order_get:1716 - 获取到 1 个待处理订单，当前活跃线程数: 0
2025-07-15 21:42:48.251 | INFO     | __main__:Run:1775 - 开始处理订单: OID=300001, 用户=17328022636, 课程ID=246628486|107051161|352747128|42670108|6a76f288656266a67fb6a4820a3e7864|5ac17897c0eea6971f688e43b15b8f51
2025-07-15 21:42:48.251 | INFO     | __main__:register_thread:67 - 注册线程: OID=300001, 当前活跃线程数: 1
2025-07-15 21:42:48.731 | SUCCESS  | __main__:Run:1819 - ID:17328022636,登录成功
2025-07-15 21:42:48.739 | INFO     | __main__:kclist:225 - ID:17328022636,平台ID 9004，使用特殊处理方式获取作业信息
2025-07-15 21:42:48.740 | SUCCESS  | __main__:kclist:242 - ID:17328022636,成功构造作业课程信息: {'kcname': '作业任务', 'courseid': '246628486|107051161|352747128|42670108|6a76f288656266a67fb6a4820a3e7864|5ac17897c0eea6971f688e43b15b8f51', 'clazzid': '246628486|107051161|352747128|42670108|6a76f288656266a67fb6a4820a3e7864|5ac17897c0eea6971f688e43b15b8f51', 'cpi': ''}
2025-07-15 21:42:48.741 | SUCCESS  | __main__:Run:1828 - ID:17328022636,课程信息匹配成功
2025-07-15 21:42:48.743 | SUCCESS  | __main__:studentstudy:935 - ID:17328022636,进度:100%,详情:作业任务_42670108 | 进行中 | 进度: 1/1 | 更新: 2025-07-15 21:42:48
2025-07-15 21:42:49.853 | INFO     | __main__:_fallback_to_traditional_homework:1496 - ID:17328022636,获取到真实作业标题: 第十五章 第一二节
2025-07-15 21:42:50.511 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1394 - ID:17328022636,提取到基本表单字段: courseId=246628486, classId=107051161, workId=42670108
2025-07-15 21:42:50.515 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1405 - ID:17328022636,找到 22 个题目项
2025-07-15 21:42:50.519 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1535 - ID:17328022636,成功提取到 22 个问题
2025-07-15 21:42:50.520 | ERROR    | API.HomeworkAI:process_homework:1025 - ID:17328022636,未生成任何答案
2025-07-15 21:42:51.172 | INFO     | API.WorkTask:Html_Wkrk:185 - ID:17328022636,尝试解析作业页面参数
2025-07-15 21:43:03.067 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:17328022636,AI返回的答案内容: 科技
2025-07-15 21:43:03.069 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A教育', 'B': 'B科技', 'C': 'C人才', 'D': 'D创新'}
2025-07-15 21:43:03.070 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 科技
2025-07-15 21:43:03.070 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:17328022636,答案与选项内容包含匹配: B
2025-07-15 21:43:03.071 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 1/22 个题目
2025-07-15 21:43:15.668 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:17328022636,AI返回的答案内容: 科技
2025-07-15 21:43:15.670 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A教育', 'B': 'B科技', 'C': 'C人才', 'D': 'D创新'}
2025-07-15 21:43:15.671 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 科技
2025-07-15 21:43:15.671 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:17328022636,答案与选项内容包含匹配: B
2025-07-15 21:43:15.672 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 2/22 个题目
2025-07-15 21:43:27.924 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:17328022636,AI返回的答案内容: 教育
2025-07-15 21:43:27.926 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A教育', 'B': 'B科技', 'C': 'C人才', 'D': 'D创新'}
2025-07-15 21:43:27.927 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 教育
2025-07-15 21:43:27.927 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:17328022636,答案与选项内容包含匹配: A
2025-07-15 21:43:27.928 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 3/22 个题目
2025-07-15 21:43:39.839 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:17328022636,AI返回的答案内容: 创新
2025-07-15 21:43:39.841 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A科技', 'B': 'B人才', 'C': 'C创新', 'D': 'D教育'}
2025-07-15 21:43:39.842 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 创新
2025-07-15 21:43:39.842 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:17328022636,答案与选项内容包含匹配: C
2025-07-15 21:43:39.842 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 4/22 个题目
2025-07-15 21:43:48.249 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 21:43:50.095 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A科技', 'B': 'B人才', 'C': 'C创新', 'D': 'D教育'}
2025-07-15 21:43:50.095 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 人才
2025-07-15 21:43:50.096 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:17328022636,答案与选项内容包含匹配: B
2025-07-15 21:43:50.096 | SUCCESS  | API.WorkTask:Html_Wkrk:486 - ID:17328022636,主题库答案匹配成功: B
2025-07-15 21:43:50.096 | INFO     | API.WorkTask:Html_Wkrk:543 - ID:17328022636,已处理 5/22 个题目
2025-07-15 21:44:02.466 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:17328022636,AI返回的答案内容: 创新创新```
2025-07-15 21:44:02.467 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A科技', 'B': 'B人才', 'C': 'C创新', 'D': 'D教育'}
2025-07-15 21:44:02.468 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 创新创新```
2025-07-15 21:44:02.468 | INFO     | API.WorkTask:Xuan:1142 - ID:17328022636,尝试使用相似度匹配
2025-07-15 21:44:02.470 | INFO     | API.WorkTask:Xuan:1162 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'创新创新'}
2025-07-15 21:44:02.473 | INFO     | API.WorkTask:Xuan:1211 - ID:17328022636,尝试使用短语匹配，答案短语: ['创新创新```']
2025-07-15 21:44:02.474 | ERROR    | API.WorkTask:Xuan:1319 - ID:17328022636,使用match_answer函数匹配失败: 'str' object has no attribute 'select_one'
2025-07-15 21:44:02.474 | WARNING  | API.WorkTask:Xuan:1323 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 21:44:02.474 | WARNING  | API.WorkTask:get_ai_answer_for_choice:1901 - ID:17328022636,AI回答无法匹配任何选项: 创新创新```
2025-07-15 21:44:02.475 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 6/22 个题目
2025-07-15 21:44:14.730 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:17328022636,AI返回的答案内容: 教育优先发展
2025-07-15 21:44:14.733 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A创新驱动发展', 'B': 'B科技自立自强', 'C': 'C人才引领驱动', 'D': 'D教育优先发展'}
2025-07-15 21:44:14.734 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 教育优先发展
2025-07-15 21:44:14.734 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:17328022636,答案与选项内容包含匹配: D
2025-07-15 21:44:14.735 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 7/22 个题目
2025-07-15 21:44:24.955 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A创新驱动发展', 'B': 'B科技自立自强', 'C': 'C人才引领驱动', 'D': 'D教育优先发展'}
2025-07-15 21:44:24.956 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 科技自立自强
2025-07-15 21:44:24.957 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:17328022636,答案与选项内容包含匹配: B
2025-07-15 21:44:24.957 | SUCCESS  | API.WorkTask:Html_Wkrk:486 - ID:17328022636,主题库答案匹配成功: B
2025-07-15 21:44:24.958 | INFO     | API.WorkTask:Html_Wkrk:543 - ID:17328022636,已处理 8/22 个题目
2025-07-15 21:44:35.181 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A创新驱动发展', 'B': 'B科技自立自强', 'C': 'C人才引领驱动', 'D': 'D教育优先发展'}
2025-07-15 21:44:35.181 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 人才引领驱动
2025-07-15 21:44:35.182 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:17328022636,答案与选项内容包含匹配: C
2025-07-15 21:44:35.183 | SUCCESS  | API.WorkTask:Html_Wkrk:486 - ID:17328022636,主题库答案匹配成功: C
2025-07-15 21:44:35.184 | INFO     | API.WorkTask:Html_Wkrk:543 - ID:17328022636,已处理 9/22 个题目
2025-07-15 21:44:39.046 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A坚持党对教育工作的全面领导', 'B': 'B坚持社会主义教育方向', 'C': 'C坚持中国特色社会主义教育道路', 'D': 'D坚持立德树人根本任务'}
2025-07-15 21:44:39.047 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 加强党对教育工作的全面领导
2025-07-15 21:44:39.047 | INFO     | API.WorkTask:Xuan:1142 - ID:17328022636,尝试使用相似度匹配
2025-07-15 21:44:39.049 | INFO     | API.WorkTask:Xuan:1162 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'加强党对教育工作的全面领导'}
2025-07-15 21:44:39.054 | INFO     | API.WorkTask:Xuan:1211 - ID:17328022636,尝试使用短语匹配，答案短语: ['加强党对教育工作的全面领导']
2025-07-15 21:44:39.054 | ERROR    | API.WorkTask:Xuan:1319 - ID:17328022636,使用match_answer函数匹配失败: 'str' object has no attribute 'select_one'
2025-07-15 21:44:39.054 | WARNING  | API.WorkTask:Xuan:1323 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 21:44:39.055 | WARNING  | API.WorkTask:Html_Wkrk:548 - ID:17328022636,主题库答案处理失败，尝试备用题库
2025-07-15 21:44:39.231 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A坚持党对教育工作的全面领导', 'B': 'B坚持社会主义教育方向', 'C': 'C坚持中国特色社会主义教育道路', 'D': 'D坚持立德树人根本任务'}
2025-07-15 21:44:39.232 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 坚持党对教育工作的全面领导
2025-07-15 21:44:39.232 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:17328022636,答案与选项内容包含匹配: A
2025-07-15 21:44:39.233 | SUCCESS  | API.WorkTask:Html_Wkrk:581 - ID:17328022636,备用题库答案匹配成功: A
2025-07-15 21:44:39.233 | INFO     | API.WorkTask:Html_Wkrk:635 - ID:17328022636,已处理 10/22 个题目
2025-07-15 21:44:48.251 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 21:44:51.764 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:17328022636,AI返回的答案内容: 党的教育方针
2025-07-15 21:44:51.766 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A科教兴国战略', 'B': 'B人才强国战略', 'C': 'C社会主义教育方针', 'D': 'D党的教育方针'}
2025-07-15 21:44:51.766 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 党的教育方针
2025-07-15 21:44:51.767 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:17328022636,答案与选项内容包含匹配: D
2025-07-15 21:44:51.767 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 11/22 个题目
2025-07-15 21:45:03.540 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:17328022636,AI返回的答案内容: 立德树人
2025-07-15 21:45:03.542 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A发展中国特色社会主义教育', 'B': 'B立德树人', 'C': 'C巩固和发展社会主义制度', 'D': 'D教育公平'}
2025-07-15 21:45:03.542 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 立德树人
2025-07-15 21:45:03.542 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:17328022636,答案与选项内容包含匹配: B
2025-07-15 21:45:03.543 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 12/22 个题目
2025-07-15 21:45:13.727 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A基础文化理论课', 'B': 'B劳动技术教育课', 'C': 'C思想政治理论课', 'D': 'D社会公德教育课'}
2025-07-15 21:45:13.728 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 思想政治理论课
2025-07-15 21:45:13.728 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:17328022636,答案与选项内容包含匹配: C
2025-07-15 21:45:13.729 | SUCCESS  | API.WorkTask:Html_Wkrk:486 - ID:17328022636,主题库答案匹配成功: C
2025-07-15 21:45:13.729 | INFO     | API.WorkTask:Html_Wkrk:543 - ID:17328022636,已处理 13/22 个题目
2025-07-15 21:45:26.087 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:17328022636,AI返回的答案内容: 坚持中国特色社会主义教育道路
2025-07-15 21:45:26.089 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A坚持党对教育工作的全面领导', 'B': 'B坚持中国特色社会主义教育道路', 'C': 'C培养理想信念坚定的社会主义建设者和接班人', 'D': 'D培养德才兼备的社会公民'}
2025-07-15 21:45:26.090 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 坚持中国特色社会主义教育道路
2025-07-15 21:45:26.090 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:17328022636,答案与选项内容包含匹配: B
2025-07-15 21:45:26.090 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 14/22 个题目
2025-07-15 21:45:36.285 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A高质量发展', 'B': 'B立德树人', 'C': 'C教育优先发展', 'D': 'D人民满意'}
2025-07-15 21:45:36.285 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 高质量发展
2025-07-15 21:45:36.286 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:17328022636,答案与选项内容包含匹配: A
2025-07-15 21:45:36.286 | SUCCESS  | API.WorkTask:Html_Wkrk:486 - ID:17328022636,主题库答案匹配成功: A
2025-07-15 21:45:36.287 | INFO     | API.WorkTask:Html_Wkrk:543 - ID:17328022636,已处理 15/22 个题目
2025-07-15 21:45:46.511 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A坚定不移推进教育公平', 'B': 'B构建全社会共同育人格局', 'C': 'C加强教育基础设施建设', 'D': 'D加强教师队伍建设'}
2025-07-15 21:45:46.511 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 培养造就新时代高水平教师队伍
2025-07-15 21:45:46.512 | INFO     | API.WorkTask:Xuan:1142 - ID:17328022636,尝试使用相似度匹配
2025-07-15 21:45:46.513 | INFO     | API.WorkTask:Xuan:1162 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'培养造就新时代高水平教师队伍'}
2025-07-15 21:45:46.517 | INFO     | API.WorkTask:Xuan:1211 - ID:17328022636,尝试使用短语匹配，答案短语: ['培养造就新时代高水平教师队伍']
2025-07-15 21:45:46.517 | ERROR    | API.WorkTask:Xuan:1319 - ID:17328022636,使用match_answer函数匹配失败: 'str' object has no attribute 'select_one'
2025-07-15 21:45:46.518 | WARNING  | API.WorkTask:Xuan:1323 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 21:45:46.518 | WARNING  | API.WorkTask:Html_Wkrk:548 - ID:17328022636,主题库答案处理失败，尝试备用题库
2025-07-15 21:45:46.712 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A坚定不移推进教育公平', 'B': 'B构建全社会共同育人格局', 'C': 'C加强教育基础设施建设', 'D': 'D加强教师队伍建设'}
2025-07-15 21:45:46.712 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 培养造就新时代高水平教师队伍
2025-07-15 21:45:46.713 | INFO     | API.WorkTask:Xuan:1142 - ID:17328022636,尝试使用相似度匹配
2025-07-15 21:45:46.715 | INFO     | API.WorkTask:Xuan:1162 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'培养造就新时代高水平教师队伍'}
2025-07-15 21:45:46.719 | INFO     | API.WorkTask:Xuan:1211 - ID:17328022636,尝试使用短语匹配，答案短语: ['培养造就新时代高水平教师队伍']
2025-07-15 21:45:46.720 | ERROR    | API.WorkTask:Xuan:1319 - ID:17328022636,使用match_answer函数匹配失败: 'str' object has no attribute 'select_one'
2025-07-15 21:45:46.720 | WARNING  | API.WorkTask:Xuan:1323 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 21:45:46.721 | WARNING  | API.WorkTask:Html_Wkrk:640 - ID:17328022636,备用题库答案处理失败，尝试AI答题
2025-07-15 21:45:48.209 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:17328022636,AI返回的答案内容: 加强教师队伍建设
2025-07-15 21:45:48.210 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A坚定不移推进教育公平', 'B': 'B构建全社会共同育人格局', 'C': 'C加强教育基础设施建设', 'D': 'D加强教师队伍建设'}
2025-07-15 21:45:48.211 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 加强教师队伍建设
2025-07-15 21:45:48.211 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:17328022636,答案与选项内容包含匹配: D
2025-07-15 21:45:48.211 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 16/22 个题目
2025-07-15 21:45:48.252 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 21:45:48.283 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A科技', 'B': 'B人才', 'C': 'C创新', 'D': 'D教育'}
2025-07-15 21:45:48.285 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 教育###科技###人才
2025-07-15 21:45:48.285 | INFO     | API.WorkTask:Xuan:1054 - ID:17328022636,多选题部分包含匹配: D - 教育
2025-07-15 21:45:48.285 | INFO     | API.WorkTask:Xuan:1054 - ID:17328022636,多选题部分包含匹配: A - 科技
2025-07-15 21:45:48.285 | INFO     | API.WorkTask:Xuan:1054 - ID:17328022636,多选题部分包含匹配: B - 人才
2025-07-15 21:45:48.286 | SUCCESS  | API.WorkTask:Xuan:1078 - ID:17328022636,多选题匹配成功: ABD
2025-07-15 21:45:48.286 | SUCCESS  | API.WorkTask:Html_Wkrk:486 - ID:17328022636,主题库答案匹配成功: ABD
2025-07-15 21:45:48.286 | INFO     | API.WorkTask:Html_Wkrk:543 - ID:17328022636,已处理 17/22 个题目
2025-07-15 21:46:00.210 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:17328022636,AI返回的答案内容: 我国教育现代化发展总体水平跨入世界中上国家行列
我国已建成世界上规模最大的教育体系
我国已经发展成为全球规模最宏大的人才资源大国
2025-07-15 21:46:00.211 | INFO     | API.WorkTask:get_ai_answer_for_choice:1784 - ID:17328022636,检测到换行符分隔的多选题答案，转换为###分隔
2025-07-15 21:46:00.211 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 18/22 个题目
2025-07-15 21:46:12.514 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:17328022636,AI返回的答案内容: 教育优先发展重在夯实人力资源深度开发基础
科技自立自强重在坚持独立自主开拓创新
人才引领驱动重在巩固发展优势赢得竞争主动
三者都紧紧围绕全面建设社会主义现代化国家战略需求
2025-07-15 21:46:12.515 | INFO     | API.WorkTask:get_ai_answer_for_choice:1784 - ID:17328022636,检测到换行符分隔的多选题答案，转换为###分隔
2025-07-15 21:46:12.516 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 19/22 个题目
2025-07-15 21:46:24.558 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:17328022636,AI返回的答案内容: 全面建成社会主义现代化强国的战略先导
实现高水平科技自立自强的重要支撑
促进全体人民共同富裕的有效途径
以中国式现代化全面推进中华民族伟大复兴的基础工程
2025-07-15 21:46:24.559 | INFO     | API.WorkTask:get_ai_answer_for_choice:1784 - ID:17328022636,检测到换行符分隔的多选题答案，转换为###分隔
2025-07-15 21:46:24.559 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 20/22 个题目
2025-07-15 21:46:36.550 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:17328022636,AI返回的答案内容: 基础性
先导性
全局性
2025-07-15 21:46:36.551 | INFO     | API.WorkTask:get_ai_answer_for_choice:1784 - ID:17328022636,检测到换行符分隔的多选题答案，转换为###分隔
2025-07-15 21:46:36.551 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 21/22 个题目
2025-07-15 21:46:48.254 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 21:46:49.440 | INFO     | API.WorkTask:get_ai_answer_for_choice:1779 - ID:17328022636,AI返回的答案内容: 培养什么人
怎样培养人
为谁培养人
2025-07-15 21:46:49.441 | INFO     | API.WorkTask:get_ai_answer_for_choice:1784 - ID:17328022636,检测到换行符分隔的多选题答案，转换为###分隔
2025-07-15 21:46:49.441 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 22/22 个题目
2025-07-15 21:46:49.442 | INFO     | API.WorkTask:Html_Wkrk:839 - ID:17328022636,所有题目处理完成，共 22/22 个
2025-07-15 21:46:49.442 | INFO     | API.WorkTask:PostDo:1385 - ID:17328022636,AI答题比例: 68.18%, AI答题数量: 15, 题库答题比例: 31.82%, 题库答题数量: 7, 总题目数量: 22
2025-07-15 21:46:49.442 | INFO     | API.WorkTask:PostDo:1410 - ID:17328022636,题库答题比例:31.82%, 使用保存模式
2025-07-15 21:46:49.944 | SUCCESS  | API.WorkTask:PostDo:1454 - ID:17328022636,保存答案成功: {"msg":"保存成功！","status":true}
2025-07-15 21:46:49.946 | INFO     | API.WorkTask:_update_progress_info:1542 - ID:17328022636,尝试导入data.Porgres模块
2025-07-15 21:46:49.946 | INFO     | API.WorkTask:_update_progress_info:1548 - ID:17328022636,成功导入data.Porgres.StartProces
2025-07-15 21:46:49.947 | INFO     | API.WorkTask:_update_progress_info:1570 - ID:17328022636,创建StartProces对象
2025-07-15 21:46:49.947 | INFO     | API.WorkTask:_update_progress_info:1583 - ID:17328022636,调用get_platform_9004_progress方法
2025-07-15 21:46:49.947 | INFO     | API.WorkTask:_update_progress_info:1595 - ID:17328022636,进度更新: 100%, 第十五章 第一二节 (已保存) | 题库答题比例:32% | AI答题比例:68%，使用保存模式 | 进度: 1/1 | 更新: 2025-07-15 21:46:49 | 题库答题比例:32% | AI答题比例:68%，使用保存模式
2025-07-15 21:46:49.948 | SUCCESS  | Config.UserSql:__init__:22 - 数据库连接池初始化成功
2025-07-15 21:46:49.948 | INFO     | API.WorkTask:_update_progress_info:1603 - ID:17328022636,更新数据库记录, oid: 300001
2025-07-15 21:46:49.968 | SUCCESS  | __main__:_fallback_to_traditional_homework:1611 - ID:17328022636,进度:100%,详情:第十五章 第一二节 | 题库答题比例:32% | AI答题比例:68%，使用保存模式 | 进度: 1/1 | 更新: 2025-07-15 21:46:49
2025-07-15 21:46:49.969 | SUCCESS  | __main__:_fallback_to_traditional_homework:1626 - ID:17328022636,传统方法作业任务 第十五章 第一二节 处理成功
2025-07-15 21:46:49.969 | INFO     | __main__:studentstudy:1050 - ID:17328022636,作业任务 作业任务_42670108 完成，等待 10 秒后处理下一任务
2025-07-15 21:46:59.978 | INFO     | __main__:unregister_thread:74 - 注销线程: OID=300001, 当前活跃线程数: 0
2025-07-15 21:47:48.255 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 21:48:48.256 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 21:49:48.258 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 21:50:48.259 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 21:51:48.260 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 21:52:48.262 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 21:53:48.263 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 21:54:48.264 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 21:55:48.264 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 21:56:48.266 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 21:57:48.267 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 21:58:48.268 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 21:59:48.269 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 22:00:46.355 | WARNING  | __main__:signal_handler:104 - 接收到终止信号，正在安全关闭...
2025-07-15 22:00:46.355 | INFO     | __main__:<module>:1945 - 程序退出
