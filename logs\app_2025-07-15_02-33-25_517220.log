2025-07-15 02:33:25.518 | INFO     | __main__:<module>:1888 - 学习通自动化系统启动...
2025-07-15 02:33:25.519 | SUCCESS  | Config.UserSql:__init__:22 - 数据库连接池初始化成功
2025-07-15 02:33:25.519 | SUCCESS  | __main__:<module>:1898 - 数据库连接池初始化成功
2025-07-15 02:33:25.526 | SUCCESS  | __main__:<module>:1919 - 数据状态重置完成
2025-07-15 02:33:25.527 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 02:33:25.527 | INFO     | __main__:<module>:1935 - 开始处理订单...
2025-07-15 02:33:25.527 | INFO     | __main__:order_get:1691 - 订单处理线程启动，最大线程数: 100
2025-07-15 02:33:25.530 | INFO     | __main__:order_get:1716 - 获取到 1 个待处理订单，当前活跃线程数: 0
2025-07-15 02:33:25.530 | INFO     | __main__:Run:1775 - 开始处理订单: OID=300001, 用户=17328022636, 课程ID=246628486|107051161|352747128|42670108|6a76f288656266a67fb6a4820a3e7864|5ac17897c0eea6971f688e43b15b8f51
2025-07-15 02:33:25.531 | INFO     | __main__:register_thread:67 - 注册线程: OID=300001, 当前活跃线程数: 1
2025-07-15 02:33:26.120 | SUCCESS  | __main__:Run:1819 - ID:17328022636,登录成功
2025-07-15 02:33:26.122 | INFO     | __main__:kclist:225 - ID:17328022636,平台ID 9004，使用特殊处理方式获取作业信息
2025-07-15 02:33:26.123 | SUCCESS  | __main__:kclist:242 - ID:17328022636,成功构造作业课程信息: {'kcname': '作业任务', 'courseid': '246628486|107051161|352747128|42670108|6a76f288656266a67fb6a4820a3e7864|5ac17897c0eea6971f688e43b15b8f51', 'clazzid': '246628486|107051161|352747128|42670108|6a76f288656266a67fb6a4820a3e7864|5ac17897c0eea6971f688e43b15b8f51', 'cpi': ''}
2025-07-15 02:33:26.123 | SUCCESS  | __main__:Run:1828 - ID:17328022636,课程信息匹配成功
2025-07-15 02:33:26.126 | SUCCESS  | __main__:studentstudy:935 - ID:17328022636,进度:100%,详情:作业任务_42670108 | 进行中 | 进度: 1/1 | 更新: 2025-07-15 02:33:26
2025-07-15 02:33:27.400 | INFO     | __main__:_fallback_to_traditional_homework:1496 - ID:17328022636,获取到真实作业标题: 第十五章 第一二节
2025-07-15 02:33:28.104 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1394 - ID:17328022636,提取到基本表单字段: courseId=246628486, classId=107051161, workId=42670108
2025-07-15 02:33:28.107 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1405 - ID:17328022636,找到 22 个题目项
2025-07-15 02:33:28.110 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1515 - ID:17328022636,成功提取到 22 个问题
2025-07-15 02:33:28.111 | ERROR    | API.HomeworkAI:process_homework:1025 - ID:17328022636,未生成任何答案
2025-07-15 02:33:28.772 | INFO     | API.WorkTask:Html_Wkrk:185 - ID:17328022636,尝试解析作业页面参数
2025-07-15 02:33:41.419 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 科技
2025-07-15 02:33:41.420 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:33:41.427 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 1/22 个题目
2025-07-15 02:33:53.560 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 科技
2025-07-15 02:33:53.561 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:33:53.562 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 2/22 个题目
2025-07-15 02:34:06.135 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 教育
2025-07-15 02:34:06.136 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:34:06.139 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 3/22 个题目
2025-07-15 02:34:19.270 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 科技
2025-07-15 02:34:19.270 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:34:19.272 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 4/22 个题目
2025-07-15 02:34:25.527 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 02:34:33.178 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 人才
2025-07-15 02:34:33.179 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:34:33.180 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 5/22 个题目
2025-07-15 02:34:48.919 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 创新
2025-07-15 02:34:48.920 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:34:48.922 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 6/22 个题目
2025-07-15 02:35:01.440 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 教育优先发展
2025-07-15 02:35:01.441 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:35:01.443 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 7/22 个题目
2025-07-15 02:35:13.540 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 科技自立自强
2025-07-15 02:35:13.541 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:35:13.543 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 8/22 个题目
2025-07-15 02:35:25.529 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 02:35:26.095 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 创新驱动发展
2025-07-15 02:35:26.096 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:35:26.098 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 9/22 个题目
2025-07-15 02:35:38.202 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 坚持党对教育工作的全面领导
2025-07-15 02:35:38.202 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:35:38.204 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 10/22 个题目
2025-07-15 02:35:55.064 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 党的教育方针
2025-07-15 02:35:55.065 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:35:55.067 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 11/22 个题目
2025-07-15 02:36:07.057 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 立德树人
2025-07-15 02:36:07.058 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:36:07.060 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 12/22 个题目
2025-07-15 02:36:19.810 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 思想政治理论课
2025-07-15 02:36:19.811 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:36:19.813 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 13/22 个题目
2025-07-15 02:36:25.530 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 02:36:32.237 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 坚持中国特色社会主义教育道路
2025-07-15 02:36:32.237 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:36:32.240 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 14/22 个题目
2025-07-15 02:36:45.002 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 高质量发展
2025-07-15 02:36:45.003 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:36:45.006 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 15/22 个题目
2025-07-15 02:36:57.147 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 加强教师队伍建设
2025-07-15 02:36:57.147 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:36:57.149 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 16/22 个题目
2025-07-15 02:36:57.383 | ERROR    | API.WorkTask:Html_Wkrk:593 - ID:17328022636,主题库查询或答案处理异常: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:37:05.663 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 科技###人才###创新
2025-07-15 02:37:05.663 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:37:05.666 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 17/22 个题目
2025-07-15 02:37:18.141 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 我国教育现代化发展总体水平跨入世界中上国家行列###我国已建成世界上规模最大的教育体系###我国已经发展成为全球规模最宏大的人才资源大国
2025-07-15 02:37:18.142 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:37:18.144 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 18/22 个题目
2025-07-15 02:37:25.531 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 02:37:30.502 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 教育优先发展重在夯实人力资源深度开发基础###科技自立自强重在坚持独立自主开拓创新###人才引领驱动重在巩固发展优势赢得竞争主动###三者都紧紧围绕全面建设社会主义现代化国家战略需求
2025-07-15 02:37:30.503 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:37:30.508 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 19/22 个题目
2025-07-15 02:37:44.245 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 全面建成社会主义现代化强国的战略先导###实现高水平科技自立自强的重要支撑###促进全体人民共同富裕的有效途径###以中国式现代化全面推进中华民族伟大复兴的基础工程
2025-07-15 02:37:44.246 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:37:44.250 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 20/22 个题目
2025-07-15 02:37:56.303 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 基础性###先导性###全局性
2025-07-15 02:37:56.304 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:37:56.306 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 21/22 个题目
2025-07-15 02:38:10.697 | INFO     | API.WorkTask:get_ai_answer_for_choice:1087 - ID:17328022636,AI返回的答案内容: 培养什么人###怎样培养人###为谁培养人
2025-07-15 02:38:10.697 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1140 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-15 02:38:10.700 | INFO     | API.WorkTask:Html_Wkrk:827 - ID:17328022636,已处理 22/22 个题目
2025-07-15 02:38:10.700 | INFO     | API.WorkTask:Html_Wkrk:880 - ID:17328022636,所有题目处理完成，共 22/22 个
2025-07-15 02:38:10.704 | INFO     | API.WorkTask:Html_Wkrk:888 - ID:17328022636,章节老师未设置内容或解析失败: 'StaratWorkTaks' object has no attribute 'PostDo'
2025-07-15 02:38:10.707 | SUCCESS  | __main__:_fallback_to_traditional_homework:1611 - ID:17328022636,进度:100%,详情:第十五章 第一二节 | 状态: 已完成 | 进度: 1/1 | 更新: 2025-07-15 02:38:10
2025-07-15 02:38:10.707 | SUCCESS  | __main__:_fallback_to_traditional_homework:1626 - ID:17328022636,传统方法作业任务 第十五章 第一二节 处理成功
2025-07-15 02:38:10.708 | INFO     | __main__:studentstudy:1050 - ID:17328022636,作业任务 作业任务_42670108 完成，等待 7 秒后处理下一任务
2025-07-15 02:38:17.727 | INFO     | __main__:unregister_thread:74 - 注销线程: OID=300001, 当前活跃线程数: 0
2025-07-15 02:38:25.533 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 02:39:25.534 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 02:40:25.535 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 02:41:25.537 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 02:41:32.690 | WARNING  | __main__:signal_handler:104 - 接收到终止信号，正在安全关闭...
2025-07-15 02:41:32.691 | INFO     | __main__:<module>:1945 - 程序退出
