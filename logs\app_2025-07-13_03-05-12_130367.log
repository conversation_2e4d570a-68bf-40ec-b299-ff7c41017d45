2025-07-13 03:05:12.131 | INFO     | __main__:<module>:1888 - 学习通自动化系统启动...
2025-07-13 03:05:12.131 | SUCCESS  | Config.UserSql:__init__:22 - 数据库连接池初始化成功
2025-07-13 03:05:12.132 | SUCCESS  | __main__:<module>:1898 - 数据库连接池初始化成功
2025-07-13 03:05:12.135 | SUCCESS  | __main__:<module>:1919 - 数据状态重置完成
2025-07-13 03:05:12.136 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-13 03:05:12.136 | INFO     | __main__:<module>:1935 - 开始处理订单...
2025-07-13 03:05:12.136 | INFO     | __main__:order_get:1691 - 订单处理线程启动，最大线程数: 100
2025-07-13 03:05:12.139 | INFO     | __main__:order_get:1716 - 获取到 1 个待处理订单，当前活跃线程数: 0
2025-07-13 03:05:12.140 | INFO     | __main__:Run:1775 - 开始处理订单: OID=300001, 用户=17328022636, 课程ID=246628486|107051161|352747128|43061896|54067247|504f8390ada2df552c9d7d4b188cc84f
2025-07-13 03:05:12.140 | INFO     | __main__:register_thread:67 - 注册线程: OID=300001, 当前活跃线程数: 1
2025-07-13 03:05:12.698 | SUCCESS  | __main__:Run:1819 - ID:17328022636,登录成功
2025-07-13 03:05:12.707 | INFO     | __main__:kclist:225 - ID:17328022636,平台ID 9004，使用特殊处理方式获取作业信息
2025-07-13 03:05:12.707 | SUCCESS  | __main__:kclist:242 - ID:17328022636,成功构造作业课程信息: {'kcname': '作业任务', 'courseid': '246628486|107051161|352747128|43061896|54067247|504f8390ada2df552c9d7d4b188cc84f', 'clazzid': '246628486|107051161|352747128|43061896|54067247|504f8390ada2df552c9d7d4b188cc84f', 'cpi': ''}
2025-07-13 03:05:12.708 | SUCCESS  | __main__:Run:1828 - ID:17328022636,课程信息匹配成功
2025-07-13 03:05:12.712 | SUCCESS  | __main__:studentstudy:935 - ID:17328022636,进度:100%,详情:作业任务_43061896 | 进行中 | 进度: 1/1 | 更新: 2025-07-13 03:05:12
2025-07-13 03:05:13.995 | INFO     | __main__:_fallback_to_traditional_homework:1496 - ID:17328022636,获取到真实作业标题: 导论 马克思主义中国化时代化的历史进程与理论成果
2025-07-13 03:05:14.840 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1394 - ID:17328022636,提取到基本表单字段: courseId=246628486, classId=107051161, workId=43061896
2025-07-13 03:05:14.845 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1405 - ID:17328022636,找到 23 个题目项
2025-07-13 03:05:14.851 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1515 - ID:17328022636,成功提取到 23 个问题
2025-07-13 03:05:14.852 | ERROR    | API.HomeworkAI:process_homework:1025 - ID:17328022636,未生成任何答案
2025-07-13 03:05:15.660 | INFO     | API.WorkTask:Html_Wkrk:185 - ID:17328022636,尝试解析作业页面参数
2025-07-13 03:05:42.672 | INFO     | API.WorkTask:get_ai_answer_for_choice:1058 - ID:17328022636,AI返回的答案内容: 党的六届六中全会
2025-07-13 03:05:42.674 | ERROR    | API.WorkTask:get_ai_answer_for_choice:1111 - ID:17328022636,调用AI生成选择题答案失败: 'StaratWorkTaks' object has no attribute 'Xuan'
2025-07-13 03:05:42.677 | INFO     | API.WorkTask:Html_Wkrk:812 - ID:17328022636,已处理 1/23 个题目
2025-07-13 03:06:01.525 | WARNING  | __main__:signal_handler:104 - 接收到终止信号，正在安全关闭...
