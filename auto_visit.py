from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import time
import requests
from io import BytesIO
import base64
import json
from datetime import datetime, timedelta



# GPT API配置
GPT_API_URL = "https://api.gptgod.online/v1/chat/completions"
GPT_API_KEY = "sk-mVlD8cVmzJASe0GJwAsrbETOSqgGo8qDdiiqd3NtxCi1sDsP"

def get_gpt_captcha_recognition(image_base64):
    """使用GPT-4识别验证码"""
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {GPT_API_KEY}'
    }
    
    system_prompt = """你是一个验证码识别专家。请识别图片中的验证码文字。
    只需返回识别出的文字内容，不需要其他解释。"""
    
    data = {
        'model': 'gpt-4o-mini',
        'messages': [
            {
                'role': 'system',
                'content': system_prompt
            },
            {
                'role': 'user',
                'content': [
                    {
                        'type': 'image_url',
                        'image_url': {
                            'url': f'data:image/png;base64,{image_base64}'
                        }
                    }
                ]
            }
        ],
        'stream': False
    }
    
    try:
        response = requests.post(GPT_API_URL, headers=headers, json=data)
        result = response.json()
        return result['choices'][0]['message']['content'].strip()
    except Exception as e:
        print(f"验证码识别出错: {str(e)}")
        return None



def countdown_timer(seconds):
    """倒计时显示"""
    end_time = datetime.now() + timedelta(seconds=seconds)
    while datetime.now() < end_time:
        remaining = end_time - datetime.now()
        remaining_seconds = int(remaining.total_seconds())
        minutes, seconds = divmod(remaining_seconds, 60)
        print(f"\r等待中: {minutes:02d}:{seconds:02d}", end="", flush=True)
        time.sleep(1)
    print("\r等待完成!            ")

# 初始化Chrome浏览器
driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()))
wait = WebDriverWait(driver, 10)

try:
    print("开始登录流程...")
    
    # 添加登录重试循环
    while True:
        # 先检查是否已经登录
        print("检查当前登录状态...")
        driver.get(LOGIN_URL)
        time.sleep(3)  # 等待页面加载
        
        if check_login_status(driver):
            print("检测到已经登录！")
            print("开始等待10秒后进行循环访问...")
            countdown_timer(10)
            visit_count = 0
            # 直接进入访问循环
            while True:
                try:
                    visit_count += 1
                    print(f"\n开始第 {visit_count} 次访问循环...")
                    driver.get(LOGIN_URL)
                    print(f"已访问 {LOGIN_URL}")
                    
                    # 等待页面加载
                    print("等待5秒让页面加载...")
                    countdown_timer(5)
                    
                    # 处理可能出现的警告框
                    try:
                        alert = driver.switch_to.alert
                        alert_text = alert.text
                        print(f"检测到警告框: {alert_text}")
                        alert.accept()
                    except:
                        pass
                    
                    # 检查登录状态
                    if not check_login_status(driver):
                        print("检测到需要重新登录...")
                        break
                    
                    print(f"访问后的URL: {driver.current_url}")
                    print(f"页面标题: {driver.title}")
                    
                    if visit_count % 10 == 0:
                        print(f"\n=== 访问统计 ===")
                        print(f"总访问次数: {visit_count}")
                        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        print(f"当前时间: {current_time}")
                        total_seconds = visit_count * 30
                        hours, remainder = divmod(total_seconds, 3600)
                        minutes, seconds = divmod(remainder, 60)
                        print(f"累计运行时间: {hours}小时{minutes}分{seconds}秒")
                        print(f"===============\n")
                    
                    print(f"第 {visit_count} 次访问完成，开始等待30秒...")
                    countdown_timer(30)
                    
                except Exception as e:
                    print(f"\n第 {visit_count} 次访问过程中出现错误: {str(e)}")
                    if not check_login_status(driver):
                        print("需要重新登录...")
                        break
                    print("等待10秒后重试...")
                    countdown_timer(10)
                    continue
            
            print("\n重新开始登录流程...")
            continue
        
        print("未登录，开始登录流程...")
        # 等待页面元素加载
        username_input = wait.until(EC.presence_of_element_located((By.ID, "admin_user")))
        password_input = driver.find_element(By.ID, "admin_pass")
        
        # 填写用户名和密码
        username_input.clear()
        username_input.send_keys(USERNAME)
        password_input.clear()
        password_input.send_keys(PASSWORD)
        
        # 检查是否存在验证码输入框
        try:
            captcha_input = driver.find_element(By.ID, "codeY")
            captcha_img = driver.find_element(By.ID, "codeimg")
            
            # 如果存在验证码，则进行验证码识别流程
            print("检测到需要输入验证码...")
            image_data = captcha_img.screenshot_as_base64
            
            # 使用GPT识别验证码
            print("正在识别验证码...")
            captcha_text = get_gpt_captcha_recognition(image_data)
            
            if captcha_text:
                print(f"识别到的验证码: {captcha_text}")
                captcha_input.clear()
                captcha_input.send_keys(captcha_text)
            else:
                print("验证码识别失败，等待3秒后重新尝试...")
                countdown_timer(3)
                continue
        except:
            print("无需输入验证码，直接登录...")
        
        # 点击登录按钮
        login_button = driver.find_element(By.ID, "login")
        login_button.click()
        
        # 等待登录结果
        time.sleep(3)
        
        # 再次检查登录状态
        if check_login_status(driver):
            print("登录成功！")
            print("开始等待10秒后进行循环访问...")
            countdown_timer(10)
            
            visit_count = 0  # 访问计数器
            
            # 登录成功后开始循环访问
            while True:
                try:
                    visit_count += 1
                    print(f"\n开始第 {visit_count} 次访问循环...")
                    driver.get(LOGIN_URL)
                    print(f"已访问 {LOGIN_URL}")
                    
                    # 等待页面加载
                    print("等待5秒让页面加载...")
                    countdown_timer(5)
                    
                    # 处理可能出现的警告框
                    try:
                        alert = driver.switch_to.alert
                        alert_text = alert.text
                        print(f"检测到警告框: {alert_text}")
                        alert.accept()
                    except:
                        pass
                    
                    # 检查登录状态
                    if not check_login_status(driver):
                        print("检测到需要重新登录...")
                        break
                    
                    print(f"访问后的URL: {driver.current_url}")
                    print(f"页面标题: {driver.title}")
                    
                    if visit_count % 10 == 0:
                        print(f"\n=== 访问统计 ===")
                        print(f"总访问次数: {visit_count}")
                        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        print(f"当前时间: {current_time}")
                        total_seconds = visit_count * 30
                        hours, remainder = divmod(total_seconds, 3600)
                        minutes, seconds = divmod(remainder, 60)
                        print(f"累计运行时间: {hours}小时{minutes}分{seconds}秒")
                        print(f"===============\n")
                    
                    print(f"第 {visit_count} 次访问完成，开始等待30秒...")
                    countdown_timer(30)
                    
                except Exception as e:
                    print(f"\n第 {visit_count} 次访问过程中出现错误: {str(e)}")
                    if not check_login_status(driver):
                        print("需要重新登录...")
                        break
                    print("等待10秒后重试...")
                    countdown_timer(10)
                    continue
            
            print("\n重新开始登录流程...")
            continue
        else:
            print("登录失败，等待3秒后重新尝试...")
            countdown_timer(3)
            continue

except KeyboardInterrupt:
    print("脚本已停止")
finally:
    driver.quit()
