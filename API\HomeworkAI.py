import re
import json
import traceback
from bs4 import BeautifulSoup
from loguru import logger
import requests
from urllib.parse import urlparse, parse_qsl, urljoin
import base64


class HomeworkAI:
    """学习通作业自动处理模块"""

    def __init__(self, session, username):
        """初始化作业处理模块"""
        self.session = session
        self.username = username
        self.user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36 Edg/117.0.2045.36"

    def get_homework_list_from_course(self, course_id, class_id=None, cpi=None):
        """获取指定课程的作业列表"""
        try:
            logger.info(f"ID:{self.username},开始获取课程 {course_id} 的作业列表")

            # 1. 首先访问作业列表页面，获取必要的参数
            base_url = (
                f"https://mooc1.chaoxing.com/mooc2/work/list?courseId={course_id}"
            )
            if class_id and cpi:
                base_url += f"&classId={class_id}&cpi={cpi}"

            logger.info(f"ID:{self.username},访问作业列表页面: {base_url}")
            response = self.session.get(
                base_url, headers={"User-Agent": self.user_agent}
            )

            if response.status_code != 200:
                logger.error(
                    f"ID:{self.username},获取作业列表页面失败，状态码: {response.status_code}"
                )
                return []

            # 2. 从页面中提取班级ID和个人ID（如果未提供）
            soup = BeautifulSoup(response.text, "html.parser")

            # 提取班级ID - 修改提取方式，更全面地查找
            if not class_id:
                # 尝试多种可能的选择器
                class_id_selectors = [
                    "input#classId",
                    "input[name='classId']",
                    "input[value][name='clazzid']",
                    "#classId",
                    "[name='classId']",
                ]

                for selector in class_id_selectors:
                    class_id_elem = soup.select_one(selector)
                    if class_id_elem and class_id_elem.has_attr("value"):
                        class_id = class_id_elem.get("value", "")
                        if class_id:
                            logger.info(
                                f"ID:{self.username},从页面提取班级ID: {class_id}"
                            )
                            break

                # 如果仍然没有找到，尝试从URL中提取
                if not class_id:
                    # 查找页面中所有包含classId参数的链接
                    for link in soup.find_all("a", href=True):
                        href = link.get("href", "")
                        class_id_match = re.search(r"[cC]lass[iI]d=(\d+)", href)
                        if class_id_match:
                            class_id = class_id_match.group(1)
                            logger.info(
                                f"ID:{self.username},从链接中提取班级ID: {class_id}"
                            )
                            break

                # 如果还是没找到，尝试从脚本中提取
                if not class_id:
                    for script in soup.find_all("script"):
                        if script.string and "classId" in script.string:
                            class_id_match = re.search(
                                r"classId\s*[:=]\s*['\"]?(\d+)['\"]?", script.string
                            )
                            if class_id_match:
                                class_id = class_id_match.group(1)
                                logger.info(
                                    f"ID:{self.username},从脚本中提取班级ID: {class_id}"
                                )
                                break

            # 提取个人ID - 修改提取方式，更全面地查找
            if not cpi:
                # 尝试多种可能的选择器
                cpi_selectors = [
                    "input#cpi",
                    "input[name='cpi']",
                    "#cpi",
                    "[name='cpi']",
                ]

                for selector in cpi_selectors:
                    cpi_elem = soup.select_one(selector)
                    if cpi_elem and cpi_elem.has_attr("value"):
                        cpi = cpi_elem.get("value", "")
                        if cpi:
                            logger.info(f"ID:{self.username},从页面提取个人ID: {cpi}")
                            break

                # 如果仍然没有找到，尝试从URL中提取
                if not cpi:
                    # 查找页面中所有包含cpi参数的链接
                    for link in soup.find_all("a", href=True):
                        href = link.get("href", "")
                        cpi_match = re.search(r"cpi=(\d+)", href)
                        if cpi_match:
                            cpi = cpi_match.group(1)
                            logger.info(f"ID:{self.username},从链接中提取个人ID: {cpi}")
                            break

                # 如果还是没找到，尝试从脚本中提取
                if not cpi:
                    for script in soup.find_all("script"):
                        if script.string and "cpi" in script.string:
                            cpi_match = re.search(
                                r"cpi\s*[:=]\s*['\"]?(\d+)['\"]?", script.string
                            )
                            if cpi_match:
                                cpi = cpi_match.group(1)
                                logger.info(
                                    f"ID:{self.username},从脚本中提取个人ID: {cpi}"
                                )
                                break

            # 如果仍然没有找到班级ID或个人ID，尝试从API获取课程信息
            if not class_id or not cpi:
                logger.info(f"ID:{self.username},尝试从API获取课程信息")
                course_info = self._get_course_info_from_list(course_id)
                if course_info:
                    if not class_id and course_info.get("class_id"):
                        class_id = course_info["class_id"]
                        logger.info(f"ID:{self.username},从API获取班级ID: {class_id}")

                    if not cpi and course_info.get("cpi"):
                        cpi = course_info["cpi"]
                        logger.info(f"ID:{self.username},从API获取个人ID: {cpi}")

            # 提取enc参数 - 参考chaoxing_homework_manager.py的实现
            enc = ""

            # 1. 首先尝试从表单输入中提取
            enc_input = soup.select_one("#enc")
            if enc_input and enc_input.has_attr("value"):
                enc = enc_input.get("value", "")
                logger.info(f"ID:{self.username},从页面表单中提取enc参数: {enc}")

            # 2. 如果没有找到，尝试从其他元素中提取
            if not enc:
                enc_selectors = ["input[name='enc']", "[name='enc']"]
                for selector in enc_selectors:
                    enc_elem = soup.select_one(selector)
                    if enc_elem and enc_elem.has_attr("value"):
                        enc = enc_elem.get("value", "")
                        if enc:
                            logger.info(
                                f"ID:{self.username},从页面元素中提取enc参数: {enc}"
                            )
                            break

            # 3. 如果仍然没有找到，尝试从URL中提取
            if not enc:
                # 查找页面中所有包含enc参数的链接
                for link in soup.find_all("a", href=True):
                    href = link.get("href", "")
                    if (
                        "work/list" in href
                        or "work/dowork" in href
                        or "work/task" in href
                    ):
                        enc_match = re.search(r"enc=([^&]+)", href)
                        if enc_match:
                            enc = enc_match.group(1)
                            logger.info(
                                f"ID:{self.username},从链接中提取enc参数: {enc}"
                            )
                            break

            # 4. 如果还是没找到，尝试从脚本中提取
            if not enc:
                for script in soup.find_all("script"):
                    if script.string and "enc" in script.string:
                        enc_match = re.search(
                            r"enc\s*[:=]\s*['\"]?([^'\",\s]+)['\"]?", script.string
                        )
                        if enc_match:
                            enc = enc_match.group(1)
                            logger.info(
                                f"ID:{self.username},从脚本中提取enc参数: {enc}"
                            )
                            break

            # 5. 如果仍然没有找到，尝试访问另一个页面获取enc
            if not enc and class_id and cpi:
                try:
                    # 尝试访问mooc1-ans域名下的作业列表页面
                    alt_url = f"https://mooc1.chaoxing.com/mooc-ans/mooc2/work/list?courseId={course_id}&classId={class_id}&cpi={cpi}"
                    logger.info(
                        f"ID:{self.username},尝试从备用URL获取enc参数: {alt_url}"
                    )
                    alt_response = self.session.get(
                        alt_url, headers={"User-Agent": self.user_agent}
                    )

                    if alt_response.status_code == 200:
                        alt_soup = BeautifulSoup(alt_response.text, "html.parser")

                        # 首先尝试从表单中提取
                        enc_input = alt_soup.select_one("#enc")
                        if enc_input and enc_input.has_attr("value"):
                            enc = enc_input.get("value", "")
                            logger.info(
                                f"ID:{self.username},从备用页面表单中提取enc参数: {enc}"
                            )

                        # 如果没有找到，尝试从链接中提取
                        if not enc:
                            for link in alt_soup.find_all("a", href=True):
                                href = link.get("href", "")
                                enc_match = re.search(r"enc=([^&]+)", href)
                                if enc_match:
                                    enc = enc_match.group(1)
                                    logger.info(
                                        f"ID:{self.username},从备用页面链接中提取enc参数: {enc}"
                                    )
                                    break

                        # 如果仍然没找到，尝试从脚本中提取
                        if not enc:
                            for script in alt_soup.find_all("script"):
                                if script.string and "enc" in script.string:
                                    enc_match = re.search(
                                        r"enc\s*[:=]\s*['\"]?([^'\",\s]+)['\"]?",
                                        script.string,
                                    )
                                    if enc_match:
                                        enc = enc_match.group(1)
                                        logger.info(
                                            f"ID:{self.username},从备用页面脚本中提取enc参数: {enc}"
                                        )
                                        break
                except Exception as e:
                    logger.warning(
                        f"ID:{self.username},尝试从备用URL获取enc参数失败: {str(e)}"
                    )

            # 6. 如果还是没找到，使用默认值
            if not enc:
                enc = "550114ba5f5bf0c61d3f650fe04350da"  # 默认值，参考chaoxing_homework_manager.py
                logger.info(f"ID:{self.username},未找到enc参数，使用默认值: {enc}")

            logger.info(f"ID:{self.username},最终使用的enc参数: {enc}")

            # 3. 构建完整的作业列表URL，参考batch_fill_homework.py的实现
            # 优先使用mooc1-ans域名，这个域名下的接口更稳定
            full_url = f"https://mooc1.chaoxing.com/mooc-ans/mooc2/work/list?courseId={course_id}"

            # 只有当class_id和cpi都不为None时才添加到URL
            if class_id:
                full_url += f"&classId={class_id}"

            if cpi:
                full_url += f"&cpi={cpi}"

            # 始终添加enc参数
            full_url += f"&enc={enc}"

            full_url += "&status=0&pageNum=1&topicId=0"

            logger.info(f"ID:{self.username},访问完整作业列表页面: {full_url}")
            full_response = self.session.get(
                full_url, headers={"User-Agent": self.user_agent}
            )

            if full_response.status_code != 200:
                # 如果访问失败，尝试使用备用域名
                backup_url = full_url.replace(
                    "mooc-ans/mooc2/work/list", "mooc2/work/list"
                )
                logger.info(
                    f"ID:{self.username},尝试访问备用作业列表页面: {backup_url}"
                )
                full_response = self.session.get(
                    backup_url, headers={"User-Agent": self.user_agent}
                )

                if full_response.status_code != 200:
                    logger.error(
                        f"ID:{self.username},获取完整作业列表页面失败，状态码: {full_response.status_code}"
                    )
                    return []

            # 4. 解析作业列表
            homework_list = []
            full_soup = BeautifulSoup(full_response.text, "html.parser")

            # 查找作业项
            homework_items = full_soup.select(".bottomList li")
            logger.info(f"ID:{self.username},找到 {len(homework_items)} 个作业项")

            for item in homework_items:
                try:
                    # 获取作业完整链接（data属性包含完整URL）
                    link = item.get("data", "")
                    if not link:
                        continue

                    # 获取作业标题
                    title_elem = item.select_one(".overHidden2")
                    title = title_elem.text.strip() if title_elem else "未知作业"

                    # 获取作业状态
                    status_elem = item.select_one(".status")
                    status = status_elem.text.strip() if status_elem else "未知状态"

                    # 从链接中提取关键参数
                    work_id_match = re.search(r"workId=(\d+)", link)
                    if not work_id_match:
                        continue

                    work_id = work_id_match.group(1)

                    # 提取其他参数
                    answer_id_match = re.search(r"answerId=(\d+)", link)
                    answer_id = answer_id_match.group(1) if answer_id_match else "0"

                    enc_match = re.search(r"enc=([^&]+)", link)
                    enc = enc_match.group(1) if enc_match else ""

                    # 保存完整作业信息，包括完整链接
                    homework_info = {
                        "workId": work_id,
                        "answerId": answer_id,
                        "enc": enc,
                        "title": title,
                        "status": status,
                        "courseId": course_id,
                        "classId": class_id,
                        "cpi": cpi,
                        "link": link,  # 保存完整链接
                    }

                    homework_list.append(homework_info)
                    logger.debug(
                        f"ID:{self.username},解析到作业: {title}, workId={work_id}, 链接={link}"
                    )

                except Exception as e:
                    logger.warning(f"ID:{self.username},解析作业项时出错: {str(e)}")

            # 5. 如果未找到作业，尝试从脚本中提取
            if not homework_list:
                logger.info(f"ID:{self.username},尝试从页面脚本中提取作业信息")

                for script in full_soup.find_all("script"):
                    if not script.string:
                        continue

                    # 查找包含作业链接的脚本
                    if "data=" in script.string or "goTask" in script.string:
                        # 提取所有可能的作业链接
                        url_patterns = [
                            r'data="(https?://[^"]+work[^"]*)"',  # 标准链接模式
                            r'goTask\("([^"]+)"\)',  # goTask函数中的链接
                        ]

                        for pattern in url_patterns:
                            urls = re.findall(pattern, script.string)
                            for url in urls:
                                # 确保这是一个作业链接
                                if "workId=" in url or "/work/" in url:
                                    # 如果链接不是以http开头，添加域名
                                    if not url.startswith("http"):
                                        url = f"https://mooc1.chaoxing.com{url if url.startswith('/') else '/'+url}"

                                    # 提取作业ID
                                    work_id_match = re.search(r"workId=(\d+)", url)
                                    if not work_id_match:
                                        work_id_match = re.search(r"/work/(\d+)", url)
                                        if not work_id_match:
                                            continue

                                    work_id = work_id_match.group(1)

                                    # 提取其他参数
                                    answer_id_match = re.search(r"answerId=(\d+)", url)
                                    answer_id = (
                                        answer_id_match.group(1)
                                        if answer_id_match
                                        else "0"
                                    )

                                    enc_match = re.search(r"enc=([^&]+)", url)
                                    enc = enc_match.group(1) if enc_match else ""

                                    # 保存作业信息
                                    homework_info = {
                                        "workId": work_id,
                                        "answerId": answer_id,
                                        "enc": enc,
                                        "title": f"作业_{work_id}",
                                        "status": "未知状态",
                                        "courseId": course_id,
                                        "classId": class_id,
                                        "cpi": cpi,
                                        "link": url,  # 保存完整链接
                                    }

                                    # 避免重复添加
                                    if not any(
                                        h["workId"] == work_id for h in homework_list
                                    ):
                                        homework_list.append(homework_info)
                                        logger.debug(
                                            f"ID:{self.username},从脚本中提取作业: workId={work_id}, 链接={url}"
                                        )

            logger.success(f"ID:{self.username},成功获取到 {len(homework_list)} 个作业")
            return homework_list

        except Exception as e:
            logger.error(f"ID:{self.username},获取作业列表异常: {str(e)}")
            traceback.print_exc()
            return []

    def _get_course_info_from_list(self, course_id):
        """从课程列表中获取指定课程的详细信息"""
        try:
            # 获取课程列表
            api = (
                "https://mooc1-api.chaoxing.com/mycourse/backclazzdata?view=json&mcode="
            )
            response = self.session.get(
                api,
                headers={
                    "User-Agent": self.user_agent,
                    "X-Requested-With": "XMLHttpRequest",
                },
            )

            if response.status_code != 200:
                logger.error(
                    f"ID:{self.username},获取课程列表失败，状态码: {response.status_code}"
                )
                return None

            data = response.json()

            if "channelList" not in data or not data["channelList"]:
                logger.error(f"ID:{self.username},课程列表为空")
                return None

            # 遍历课程列表，查找匹配的课程
            for channel in data["channelList"]:
                try:
                    if (
                        "content" not in channel
                        or "course" not in channel["content"]
                        or "data" not in channel["content"]["course"]
                        or not channel["content"]["course"]["data"]
                    ):
                        continue

                    course_data = channel["content"]["course"]["data"][0]
                    current_course_id = course_data["id"]

                    # 判断课程ID是否匹配
                    if str(course_id) == str(current_course_id):
                        course_name = course_data["name"]
                        class_id = channel["content"]["id"]
                        cpi = channel["content"]["cpi"]

                        logger.info(
                            f"ID:{self.username},找到匹配课程: {course_name}, 班级ID: {class_id}, 个人ID: {cpi}"
                        )
                        return {
                            "course_name": course_name,
                            "course_id": current_course_id,
                            "class_id": class_id,
                            "cpi": cpi,
                        }
                except Exception as e:
                    logger.warning(f"ID:{self.username},处理课程信息时出错: {str(e)}")
                    continue

            logger.warning(f"ID:{self.username},未找到课程ID: {course_id}")
            return None

        except Exception as e:
            logger.error(f"ID:{self.username},获取课程信息异常: {str(e)}")
            traceback.print_exc()
            return None

    def _parse_homework_list_from_html(
        self, html_content, course_id, class_id=None, cpi=None
    ):
        """从HTML内容中解析作业列表"""
        try:
            soup = BeautifulSoup(html_content, "html.parser")

            # 从页面中提取班级ID和个人ID（如果未提供）
            if not class_id:
                # 尝试多种可能的选择器
                class_id_selectors = [
                    "input#classId",
                    "input[name='classId']",
                    "input[value][name='clazzid']",
                    "#classId",
                    "[name='classId']",
                ]

                for selector in class_id_selectors:
                    class_id_elem = soup.select_one(selector)
                    if class_id_elem and class_id_elem.has_attr("value"):
                        class_id = class_id_elem.get("value", "")
                        if class_id:
                            logger.info(
                                f"ID:{self.username},从页面提取班级ID: {class_id}"
                            )
                            break

                # 如果仍然没有找到，尝试从URL中提取
                if not class_id:
                    # 查找页面中所有包含classId参数的链接
                    for link in soup.find_all("a", href=True):
                        href = link.get("href", "")
                        class_id_match = re.search(r"[cC]lass[iI]d=(\d+)", href)
                        if class_id_match:
                            class_id = class_id_match.group(1)
                            logger.info(
                                f"ID:{self.username},从链接中提取班级ID: {class_id}"
                            )
                            break

                # 如果还是没找到，尝试从脚本中提取
                if not class_id:
                    for script in soup.find_all("script"):
                        if script.string and "classId" in script.string:
                            class_id_match = re.search(
                                r"classId\s*[:=]\s*['\"]?(\d+)['\"]?", script.string
                            )
                            if class_id_match:
                                class_id = class_id_match.group(1)
                                logger.info(
                                    f"ID:{self.username},从脚本中提取班级ID: {class_id}"
                                )
                                break

            if not cpi:
                # 尝试多种可能的选择器
                cpi_selectors = [
                    "input#cpi",
                    "input[name='cpi']",
                    "#cpi",
                    "[name='cpi']",
                ]

                for selector in cpi_selectors:
                    cpi_elem = soup.select_one(selector)
                    if cpi_elem and cpi_elem.has_attr("value"):
                        cpi = cpi_elem.get("value", "")
                        if cpi:
                            logger.info(f"ID:{self.username},从页面提取个人ID: {cpi}")
                            break

                # 如果仍然没有找到，尝试从URL中提取
                if not cpi:
                    # 查找页面中所有包含cpi参数的链接
                    for link in soup.find_all("a", href=True):
                        href = link.get("href", "")
                        cpi_match = re.search(r"cpi=(\d+)", href)
                        if cpi_match:
                            cpi = cpi_match.group(1)
                            logger.info(f"ID:{self.username},从链接中提取个人ID: {cpi}")
                            break

                # 如果还是没找到，尝试从脚本中提取
                if not cpi:
                    for script in soup.find_all("script"):
                        if script.string and "cpi" in script.string:
                            cpi_match = re.search(
                                r"cpi\s*[:=]\s*['\"]?(\d+)['\"]?", script.string
                            )
                            if cpi_match:
                                cpi = cpi_match.group(1)
                                logger.info(
                                    f"ID:{self.username},从脚本中提取个人ID: {cpi}"
                                )
                                break

            # 如果仍然没有找到班级ID或个人ID，尝试从API获取课程信息
            if not class_id or not cpi:
                logger.info(f"ID:{self.username},尝试从API获取课程信息")
                course_info = self._get_course_info_from_list(course_id)
                if course_info:
                    if not class_id and course_info.get("class_id"):
                        class_id = course_info["class_id"]
                        logger.info(f"ID:{self.username},从API获取班级ID: {class_id}")

                    if not cpi and course_info.get("cpi"):
                        cpi = course_info["cpi"]
                        logger.info(f"ID:{self.username},从API获取个人ID: {cpi}")

            # 提取enc参数 - 参考chaoxing_homework_manager.py的实现
            enc = ""

            # 1. 首先尝试从表单输入中提取
            enc_input = soup.select_one("#enc")
            if enc_input and enc_input.has_attr("value"):
                enc = enc_input.get("value", "")
                logger.info(f"ID:{self.username},从页面表单中提取enc参数: {enc}")

            # 2. 如果没有找到，尝试从其他元素中提取
            if not enc:
                enc_selectors = ["input[name='enc']", "[name='enc']"]
                for selector in enc_selectors:
                    enc_elem = soup.select_one(selector)
                    if enc_elem and enc_elem.has_attr("value"):
                        enc = enc_elem.get("value", "")
                        if enc:
                            logger.info(
                                f"ID:{self.username},从页面元素中提取enc参数: {enc}"
                            )
                            break

            # 3. 如果仍然没有找到，尝试从URL中提取
            if not enc:
                # 查找页面中所有包含enc参数的链接
                for link in soup.find_all("a", href=True):
                    href = link.get("href", "")
                    if (
                        "work/list" in href
                        or "work/dowork" in href
                        or "work/task" in href
                    ):
                        enc_match = re.search(r"enc=([^&]+)", href)
                        if enc_match:
                            enc = enc_match.group(1)
                            logger.info(
                                f"ID:{self.username},从链接中提取enc参数: {enc}"
                            )
                            break

            # 4. 如果还是没找到，尝试从脚本中提取
            if not enc:
                for script in soup.find_all("script"):
                    if script.string and "enc" in script.string:
                        enc_match = re.search(
                            r"enc\s*[:=]\s*['\"]?([^'\",\s]+)['\"]?", script.string
                        )
                        if enc_match:
                            enc = enc_match.group(1)
                            logger.info(
                                f"ID:{self.username},从脚本中提取enc参数: {enc}"
                            )
                            break

            # 5. 如果还是没找到，使用默认值
            if not enc:
                enc = "550114ba5f5bf0c61d3f650fe04350da"  # 默认值，参考chaoxing_homework_manager.py
                logger.info(f"ID:{self.username},未找到enc参数，使用默认值: {enc}")

            logger.info(f"ID:{self.username},最终使用的enc参数: {enc}")

            # 构建完整的作业列表URL
            full_url = f"https://mooc1.chaoxing.com/mooc-ans/mooc2/work/list?courseId={course_id}"

            # 只有当class_id和cpi都不为None时才添加到URL
            if class_id:
                full_url += f"&classId={class_id}"

            if cpi:
                full_url += f"&cpi={cpi}"

            # 始终添加enc参数
            full_url += f"&enc={enc}"

            full_url += "&status=0&pageNum=1&topicId=0"

            logger.info(f"ID:{self.username},构建完整作业列表URL: {full_url}")

            # 解析作业列表
            homework_list = []

            # 查找作业项
            homework_items = soup.select(".bottomList li")
            logger.info(f"ID:{self.username},找到 {len(homework_items)} 个作业项")

            for item in homework_items:
                try:
                    # 获取作业完整链接（data属性包含完整URL）
                    link = item.get("data", "")
                    if not link:
                        continue

                    # 获取作业标题
                    title_elem = item.select_one(".overHidden2")
                    title = title_elem.text.strip() if title_elem else "未知作业"

                    # 获取作业状态
                    status_elem = item.select_one(".status")
                    status = status_elem.text.strip() if status_elem else "未知状态"

                    # 从链接中提取关键参数
                    work_id_match = re.search(r"workId=(\d+)", link)
                    if not work_id_match:
                        continue

                    work_id = work_id_match.group(1)

                    # 提取其他参数
                    answer_id_match = re.search(r"answerId=(\d+)", link)
                    answer_id = answer_id_match.group(1) if answer_id_match else "0"

                    enc_match = re.search(r"enc=([^&]+)", link)
                    link_enc = (
                        enc_match.group(1) if enc_match else enc
                    )  # 使用之前提取的enc作为备选

                    # 保存完整作业信息，包括完整链接
                    homework_info = {
                        "workId": work_id,
                        "answerId": answer_id,
                        "enc": link_enc,
                        "title": title,
                        "status": status,
                        "courseId": course_id,
                        "classId": class_id,
                        "cpi": cpi,
                        "link": link,  # 保存完整链接
                    }

                    homework_list.append(homework_info)
                    logger.debug(
                        f"ID:{self.username},解析到作业: {title}, workId={work_id}, 链接={link}"
                    )

                except Exception as e:
                    logger.warning(f"ID:{self.username},解析作业项时出错: {str(e)}")

            # 如果未找到作业，尝试从脚本中提取
            if not homework_list:
                logger.info(f"ID:{self.username},尝试从页面脚本中提取作业信息")

                for script in soup.find_all("script"):
                    if not script.string:
                        continue

                    # 查找包含作业链接的脚本
                    if "data=" in script.string or "goTask" in script.string:
                        # 提取所有可能的作业链接
                        url_patterns = [
                            r'data="(https?://[^"]+work[^"]*)"',  # 标准链接模式
                            r'goTask\("([^"]+)"\)',  # goTask函数中的链接
                        ]

                        for pattern in url_patterns:
                            urls = re.findall(pattern, script.string)
                            for url in urls:
                                # 确保这是一个作业链接
                                if "workId=" in url or "/work/" in url:
                                    # 如果链接不是以http开头，添加域名
                                    if not url.startswith("http"):
                                        url = f"https://mooc1.chaoxing.com{url if url.startswith('/') else '/'+url}"

                                    # 提取作业ID
                                    work_id_match = re.search(r"workId=(\d+)", url)
                                    if not work_id_match:
                                        work_id_match = re.search(r"/work/(\d+)", url)
                                        if not work_id_match:
                                            continue

                                    work_id = work_id_match.group(1)

                                    # 提取其他参数
                                    answer_id_match = re.search(r"answerId=(\d+)", url)
                                    answer_id = (
                                        answer_id_match.group(1)
                                        if answer_id_match
                                        else "0"
                                    )

                                    enc_match = re.search(r"enc=([^&]+)", url)
                                    url_enc = (
                                        enc_match.group(1) if enc_match else enc
                                    )  # 使用之前提取的enc作为备选

                                    # 保存作业信息
                                    homework_info = {
                                        "workId": work_id,
                                        "answerId": answer_id,
                                        "enc": url_enc,
                                        "title": f"作业_{work_id}",
                                        "status": "未知状态",
                                        "courseId": course_id,
                                        "classId": class_id,
                                        "cpi": cpi,
                                        "link": url,  # 保存完整链接
                                    }

                                    # 避免重复添加
                                    if not any(
                                        h["workId"] == work_id for h in homework_list
                                    ):
                                        homework_list.append(homework_info)
                                        logger.debug(
                                            f"ID:{self.username},从脚本中提取作业: workId={work_id}, 链接={url}"
                                        )

            return homework_list
        except Exception as e:
            logger.error(f"ID:{self.username},解析作业列表HTML异常: {str(e)}")
            traceback.print_exc()
            return []

    def process_homework(self, homework_url, platform_id=None):
        """处理作业，获取题目并提交答案"""
        try:
            # 判断是否需要减少日志输出
            reduce_logs = platform_id == 9004

            # 仅在非9004平台ID时输出详细日志
            if not reduce_logs:
                logger.info(f"ID:{self.username},开始处理作业: {homework_url}")

            # 记录平台ID
            self.current_platform_id = platform_id
            # 仅在非9004平台ID时输出详细日志
            if not reduce_logs:
                logger.info(f"ID:{self.username},当前平台ID: {platform_id}")

            # 1. 获取作业HTML内容
            try:
                # 处理URL，确保格式正确
                if "http" not in homework_url:
                    homework_url = f"https://mooc1.chaoxing.com{homework_url}"

                # 设置请求头
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
                    "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
                    "Referer": "https://mooc1.chaoxing.com/",
                }

                # 发送请求获取作业页面
                response = self.session.get(
                    homework_url, headers=headers, allow_redirects=True
                )

                # 检查是否有重定向
                if response.history:
                    # 仅在非9004平台ID时输出详细日志
                    if not reduce_logs:
                        logger.info(f"ID:{self.username},请求被重定向: {response.url}")
                    homework_url = response.url

                # 检查响应状态
                if response.status_code != 200:
                    # 错误日志总是输出，无论平台ID
                    logger.error(
                        f"ID:{self.username},获取作业页面失败，状态码: {response.status_code}"
                    )
                    return False

                # 获取HTML内容
                html_content = response.text

                # 检查HTML内容是否包含作业题目
                if "questionLi" not in html_content and "TiMu" not in html_content:
                    # 仅在非9004平台ID时输出详细日志
                    if not reduce_logs:
                        logger.warning(
                            f"ID:{self.username},HTML内容中未找到作业题目，尝试提取作业链接"
                        )

                    # 尝试从页面中提取作业链接
                    soup = BeautifulSoup(html_content, "html.parser")
                    homework_links = []

                    # 查找可能包含作业链接的元素
                    for a in soup.find_all("a", href=True):
                        href = a.get("href", "")
                        if "work" in href and "task" in href:
                            full_url = (
                                href
                                if "http" in href
                                else f"https://mooc1.chaoxing.com{href}"
                            )
                            homework_links.append(full_url)

                    # 如果找到作业链接，使用第一个链接
                    if homework_links:
                        # 仅在非9004平台ID时输出详细日志
                        if not reduce_logs:
                            logger.info(
                                f"ID:{self.username},找到作业链接: {homework_links[0]}"
                            )
                        homework_url = homework_links[0]

                        # 重新获取作业页面
                        response = self.session.get(homework_url, headers=headers)
                        html_content = response.text
                    else:
                        # 错误日志总是输出，无论平台ID
                        logger.error(f"ID:{self.username},未找到有效的作业链接")
                        return False

                # 仅在非9004平台ID时输出详细日志
                if not reduce_logs:
                    logger.info(
                        f"ID:{self.username},成功获取作业HTML内容，长度: {len(html_content)}"
                    )
            except Exception as e:
                # 错误日志总是输出，无论平台ID
                logger.error(f"ID:{self.username},获取作业HTML内容异常: {str(e)}")
                traceback.print_exc()
                return False

            # 2. 提取表单数据和问题
            form_data, question_ids, question_data = (
                self._extract_form_data_and_questions(html_content)
            )

            # 如果是平台ID 9004，从URL中提取额外的参数
            if platform_id == 9004:
                try:
                    # 从URL中提取参数
                    from urllib.parse import urlparse, parse_qs

                    parsed_url = urlparse(homework_url)
                    query_params = parse_qs(parsed_url.query)

                    # 添加或更新表单数据
                    if "courseId" in query_params and "courseId" not in form_data:
                        form_data["courseId"] = query_params["courseId"][0]
                    if "classId" in query_params and "classId" not in form_data:
                        form_data["classId"] = query_params["classId"][0]
                    if "workId" in query_params and "workRelationId" not in form_data:
                        form_data["workRelationId"] = query_params["workId"][0]
                    if "answerId" in query_params and "workAnswerId" not in form_data:
                        form_data["workAnswerId"] = query_params["answerId"][0]
                    if "enc" in query_params and "enc_work" not in form_data:
                        form_data["enc_work"] = query_params["enc"][0]
                    if "cpi" in query_params and "cpi" not in form_data:
                        form_data["cpi"] = query_params["cpi"][0]

                    # 仅在非9004平台ID时输出详细日志
                    if not reduce_logs:
                        logger.info(
                            f"ID:{self.username},从URL提取参数成功，表单数据: {form_data}"
                        )
                except Exception as e:
                    # 错误日志总是输出，无论平台ID
                    logger.error(f"ID:{self.username},从URL提取参数异常: {str(e)}")

            # 检查是否成功提取表单数据和问题
            if not form_data or not question_ids:
                # 错误日志总是输出，无论平台ID
                logger.error(f"ID:{self.username},未能提取表单数据或问题")
                return False

            # 仅在非9004平台ID时输出详细日志
            if not reduce_logs:
                logger.info(
                    f"ID:{self.username},成功提取表单数据和问题，题目数量: {len(question_ids)}"
                )

            # 3. 生成答案
            answers = {}
            for question_id in question_ids:
                question_info = question_data.get(question_id, {})
                question_text = question_info.get("plain_text", "")
                question_type = question_info.get("type", 4)  # 默认为简答题

                # 生成答案
                answer = self._generate_answers(
                    question_text, question_type, question_info
                )

                if answer:
                    answers[question_id] = answer
                    # 仅在非9004平台ID时输出详细日志
                    if not reduce_logs:
                        logger.info(
                            f"ID:{self.username},题目 {question_id}: {question_text[:20]}... 答案: {answer[:20]}..."
                        )
                # else:
                #     # 警告日志总是输出，无论平台ID
                #     logger.warning(
                #         f"ID:{self.username},题目 {question_id}: {question_text[:20]}... 未能生成答案"
                #     )

            # 4. 提交答案
            if answers:
                # 仅在非9004平台ID时输出详细日志
                if not reduce_logs:
                    logger.info(
                        f"ID:{self.username},准备提交答案，题目数量: {len(question_ids)}，已生成答案数量: {len(answers)}"
                    )
                result = self._submit_homework_answers(
                    form_data, question_ids, answers, homework_url
                )
                if result:
                    # 成功日志总是输出，无论平台ID
                    logger.success(f"ID:{self.username},作业提交成功")
                    return True
                else:
                    # 错误日志总是输出，无论平台ID
                    logger.error(f"ID:{self.username},作业提交失败")
                    return False
            else:
                # 错误日志总是输出，无论平台ID
                logger.error(f"ID:{self.username},未生成任何答案")
                return False

        except Exception as e:
            # 错误日志总是输出，无论平台ID
            logger.error(f"ID:{self.username},处理作业异常: {str(e)}")
            traceback.print_exc()
            return False

    def _generate_answers(self, question_text, question_type, question_info):
        """根据问题生成答案"""
        try:
            # 在方法开始时导入requests模块，避免作用域问题
            import requests

            # 判断是否为简答题
            if question_type == 4:  # 简答题
                logger.info(f"ID:{self.username},检测到简答题，尝试使用AI生成答案")
                try:
                    # 预处理问题文本，去除HTML标签和特殊格式
                    clean_question = re.sub(r"<.*?>", "", question_text)  # 去除HTML标签

                    # 处理"###"符号，通常表示填空符
                    if "###" in clean_question:
                        clean_question = clean_question.replace("###", "_____")

                    # 去除题目重复的问题（如果问题本身包含了问题内容）
                    question_parts = clean_question.split("?")
                    if len(question_parts) > 1:
                        # 如果问题中有问号，只取第一个问号前的内容作为主要问题
                        main_question = question_parts[0] + "?"

                        # 检查后面的内容是否与前面重复
                        if main_question.strip() in "".join(question_parts[1:]).strip():
                            clean_question = main_question

                    # 如果问题太长，截取前200个字符
                    if len(clean_question) > 200:
                        clean_question = clean_question[:200] + "..."

                    # 检查是否包含图片，并且是平台ID 9004
                    has_image = False
                    image_urls = []

                    # 从原始问题文本中提取图片URL
                    if "<img" in question_info.get("text", ""):
                        img_tags = re.findall(
                            r'<img[^>]+src="([^"]+)"', question_info.get("text", "")
                        )
                        if img_tags:
                            has_image = True
                            image_urls = img_tags
                            logger.info(
                                f"ID:{self.username},检测到图文类型简答题，图片URL: {image_urls}"
                            )

                    # 如果是平台ID 9004且包含图片，使用GPT图像识别API
                    if (
                        has_image
                        and hasattr(self, "current_platform_id")
                        and self.current_platform_id == 9004
                    ):
                        logger.info(
                            f"ID:{self.username},平台ID 9004的图文类型简答题，使用GPT图像识别API"
                        )

                        # 使用auto_visit.py中的GPT API配置
                        GPT_API_URL = "https://api.gptgod.online/v1/chat/completions"
                        GPT_API_KEY = (
                            "sk-mVlD8cVmzJASe0GJwAsrbETOSqgGo8qDdiiqd3NtxCi1sDsP"
                        )

                        # 获取图片的base64编码
                        image_base64 = None
                        try:
                            # 尝试获取第一张图片的内容
                            image_url = image_urls[0]
                            # 使用session而不是直接使用requests，以便复用会话和cookies
                            image_response = self.session.get(image_url, timeout=10)
                            if image_response.status_code == 200:
                                image_base64 = base64.b64encode(
                                    image_response.content
                                ).decode("utf-8")
                                logger.info(
                                    f"ID:{self.username},成功获取图片内容并转换为base64"
                                )
                        except Exception as e:
                            logger.error(
                                f"ID:{self.username},获取图片内容失败: {str(e)}"
                            )
                            image_base64 = None

                        if image_base64:
                            # 准备GPT请求
                            headers = {
                                "Content-Type": "application/json",
                                "Authorization": f"Bearer {GPT_API_KEY}",
                            }

                            system_prompt = """你是专注解答图文简答题的工具，需严格依据题目图片与问题内容，直接输出简洁、精准答案，无需推导过程。
                            先快速识别题目学科（如力学、历史、语文等）、题型（计算、概念、分析等），
                            提取关键条件，精准匹配知识点得出答案，答案务必简洁清晰，严格契合题目问法与图文信息，无关内容一律不出现 。"""

                            data = {
                                "model": "gpt-4o-mini",
                                "messages": [
                                    {"role": "system", "content": system_prompt},
                                    {
                                        "role": "user",
                                        "content": [
                                            {
                                                "type": "text",
                                                "text": f"请解答以下问题: {clean_question}",
                                            },
                                            {
                                                "type": "image_url",
                                                "image_url": {
                                                    "url": f"data:image/png;base64,{image_base64}"
                                                },
                                            },
                                        ],
                                    },
                                ],
                                "stream": False,
                            }

                            try:
                                logger.info(f"ID:{self.username},发送GPT图像识别请求")
                                # 使用前面导入的requests模块
                                response = requests.post(
                                    GPT_API_URL, headers=headers, json=data, timeout=60
                                )
                                result = response.json()

                                if "choices" in result and len(result["choices"]) > 0:
                                    ai_answer = result["choices"][0]["message"][
                                        "content"
                                    ].strip()

                                    # 处理AI回答
                                    # 如果回答以问题开头，去除问题部分
                                    question_keywords = [
                                        "问题:",
                                        "题目:",
                                        "Question:",
                                        "问：",
                                    ]
                                    for keyword in question_keywords:
                                        if ai_answer.startswith(keyword):
                                            answer_parts = ai_answer.split("\n", 1)
                                            if len(answer_parts) > 1:
                                                ai_answer = answer_parts[1].strip()

                                    logger.success(
                                        f"ID:{self.username},GPT图像识别生成答案成功: {ai_answer[:50]}..."
                                    )
                                    return ai_answer
                                else:
                                    logger.warning(
                                        f"ID:{self.username},GPT图像识别API返回无效结果: {result}"
                                    )
                                    # 如果GPT图像识别失败，回退到普通AI接口
                            except Exception as e:
                                logger.error(
                                    f"ID:{self.username},调用GPT图像识别API失败: {str(e)}"
                                )
                                # 如果GPT图像识别失败，回退到普通AI接口

                    # 调用普通AI接口获取答案（对于非图文题或非9004平台，或者图像识别失败的情况）
                    # 使用前面导入的requests模块

                    # AI接口配置
                    AI_API_URL = "http://tk.mixuelo.cc/api.php?act=aimodel"
                    AI_API_KEY = "zXPX828s29Kk7Yj2"
                    AI_MODEL = "deepseek-chat"

                    # 准备请求数据
                    data = {
                        "key": AI_API_KEY,
                        "model": AI_MODEL,
                        "question": clean_question,
                        "prompt": "你是一个专业的学习助手，请根据问题提供准确、简洁的答案，不要重复问题内容，直接给出答案。",
                    }

                    # 发送请求
                    headers = {
                        "Content-Type": "application/x-www-form-urlencoded",
                        "Authorization": f"Bearer {AI_API_KEY}",
                    }

                    response = requests.post(
                        AI_API_URL, data=data, headers=headers, timeout=30
                    )
                    result = response.json()

                    # 检查响应
                    if result.get("code") == 1:
                        # 成功响应，尝试从data或answer字段获取答案
                        if "answer" in result:
                            ai_answer = result["answer"]
                        elif "data" in result:
                            ai_answer = result["data"]
                        else:
                            logger.warning(
                                f"ID:{self.username},AI接口返回格式异常: {result}"
                            )
                            return "此题暂无标准答案，请根据所学知识作答。"

                        # 处理AI回答中可能的问题重复
                        if clean_question.strip() in ai_answer:
                            # 如果AI回答包含了问题内容，尝试去除
                            answer_parts = ai_answer.split(clean_question.strip())
                            if len(answer_parts) > 1:
                                ai_answer = "".join(answer_parts[1:]).strip()

                        # 如果回答以问题开头，去除问题部分
                        question_keywords = ["问题:", "题目:", "Question:", "问："]
                        for keyword in question_keywords:
                            if ai_answer.startswith(keyword):
                                answer_parts = ai_answer.split("\n", 1)
                                if len(answer_parts) > 1:
                                    ai_answer = answer_parts[1].strip()

                        # 如果回答包含"答案："等前缀，只保留答案部分
                        answer_prefixes = ["答案:", "答案：", "Answer:", "答："]
                        for prefix in answer_prefixes:
                            if prefix in ai_answer:
                                parts = ai_answer.split(prefix, 1)
                                if len(parts) > 1:
                                    ai_answer = parts[1].strip()

                        # 确保答案不包含HTML标签和"###"符号
                        ai_answer = re.sub(r"<.*?>", "", ai_answer)
                        ai_answer = ai_answer.replace("###", "")

                        logger.success(
                            f"ID:{self.username},AI生成答案成功: {ai_answer[:50]}..."
                        )
                        return ai_answer
                    else:
                        error_msg = result.get("msg", "未知错误")
                        logger.warning(
                            f"ID:{self.username},AI接口返回错误: {error_msg}"
                        )
                        return "此题暂无标准答案，请根据所学知识作答。"
                except Exception as e:
                    logger.error(f"ID:{self.username},调用AI接口生成答案失败: {str(e)}")
                    return "此题暂无标准答案，请根据所学知识作答。"

            # 非简答题使用常规题库填充
            elif question_type == 0:  # 单选题
                return ""  # 默认选A
            elif question_type == 1:  # 多选题
                return ""  # 默认选AB
            elif question_type == 2:  # 判断题
                return ""  # 默认选对
            elif question_type == 3:  # 填空题
                return ""
            else:
                return ""  # 其他类型默认返回A
        except Exception as e:
            logger.error(f"ID:{self.username},生成答案异常: {str(e)}")
            return None

    def _extract_form_data_and_questions(self, html_content):
        """从HTML页面中提取表单数据和问题"""
        try:
            soup = BeautifulSoup(html_content, "html.parser")

            # 1. 提取表单基本字段，严格参考batch_fill_homework.py的实现
            try:
                # 尝试提取基本表单字段
                course_id = ""
                class_id = ""
                cpi = ""
                work_id = ""
                answer_id = ""
                standard_enc = ""
                enc_work = ""
                total_question_num = ""

                # 提取courseId
                course_id_elem = soup.select_one("#courseId")
                if course_id_elem and course_id_elem.has_attr("value"):
                    course_id = course_id_elem.get("value", "")

                # 提取classId
                class_id_elem = soup.select_one("#classId")
                if class_id_elem and class_id_elem.has_attr("value"):
                    class_id = class_id_elem.get("value", "")

                # 提取cpi
                cpi_elem = soup.select_one("#cpi")
                if cpi_elem and cpi_elem.has_attr("value"):
                    cpi = cpi_elem.get("value", "")

                # 提取workId
                work_id_elem = soup.select_one("#workId")
                if work_id_elem and work_id_elem.has_attr("value"):
                    work_id = work_id_elem.get("value", "")

                # 提取answerId
                answer_id_elem = soup.select_one("#answerId")
                if answer_id_elem and answer_id_elem.has_attr("value"):
                    answer_id = answer_id_elem.get("value", "")

                # 提取standardEnc
                standard_enc_elem = soup.select_one("#standardEnc")
                if standard_enc_elem and standard_enc_elem.has_attr("value"):
                    standard_enc = standard_enc_elem.get("value", "")

                # 提取enc_work
                enc_work_elem = soup.select_one("#enc_work")
                if enc_work_elem and enc_work_elem.has_attr("value"):
                    enc_work = enc_work_elem.get("value", "")
                else:
                    # 尝试从enc字段获取
                    enc_elem = soup.select_one("#enc")
                    if enc_elem and enc_elem.has_attr("value"):
                        enc_work = enc_elem.get("value", "")

                # 提取totalQuestionNum
                total_question_num_elem = soup.select_one("#totalQuestionNum")
                if total_question_num_elem and total_question_num_elem.has_attr(
                    "value"
                ):
                    total_question_num = total_question_num_elem.get("value", "")
                else:
                    total_question_num = "6eaca1bf84b6b650bc4b09d8f8805f1e"  # 默认值

                # 如果没有找到某些字段，尝试从URL参数中提取
                if not all([course_id, class_id, work_id]):
                    # 查找所有包含作业URL的链接
                    for link in soup.find_all("a", href=True):
                        href = link.get("href", "")
                        if "work" in href and "task" in href:
                            # 提取URL参数
                            params = dict(parse_qsl(urlparse(href).query))
                            if not course_id and "courseId" in params:
                                course_id = params["courseId"]
                            if not class_id and "classId" in params:
                                class_id = params["classId"]
                            if not work_id and "workId" in params:
                                work_id = params["workId"]
                            if not answer_id and "answerId" in params:
                                answer_id = params["answerId"]
                            if not enc_work and "enc" in params:
                                enc_work = params["enc"]
                            if not cpi and "cpi" in params:
                                cpi = params["cpi"]

                # 构建表单数据，严格按照batch_fill_homework.py的格式
                form_data = {
                    "courseId": course_id,
                    "classId": class_id,
                    "knowledgeid": "0",
                    "cpi": cpi,
                    "workRelationId": work_id,
                    "workAnswerId": answer_id,
                    "jobid": "",
                    "standardEnc": standard_enc,
                    "enc_work": enc_work,
                    "totalQuestionNum": total_question_num,
                    "pyFlag": "1",  # 保存模式
                    "mooc2": "1",
                    "randomOptions": "false",
                    "workTimesEnc": "",
                }

                logger.info(
                    f"ID:{self.username},提取到基本表单字段: courseId={course_id}, classId={class_id}, workId={work_id}"
                )

            except Exception as e:
                logger.error(f"ID:{self.username},提取表单字段异常: {str(e)}")
                form_data = {}

            # 2. 提取题目信息
            # 首先查找所有题目项
            question_items = soup.select(".questionLi")
            logger.info(f"ID:{self.username},找到 {len(question_items)} 个题目项")

            # 如果没有找到题目，尝试其他选择器
            if not question_items:
                question_items = soup.select(".TiMu")
                logger.info(
                    f"ID:{self.username},使用备选选择器找到 {len(question_items)} 个题目项"
                )

            # 如果仍然没有找到题目，尝试更多选择器
            if not question_items:
                question_items = soup.select("[data]")  # 尝试查找带有data属性的元素
                logger.info(
                    f"ID:{self.username},使用data属性选择器找到 {len(question_items)} 个题目项"
                )

            # 解析每个题目
            question_data = {}
            question_ids = []

            for item in question_items:
                try:
                    # 获取题目ID
                    question_id = item.get("data") or ""
                    if not question_id:
                        # 如果没有data属性，尝试从其他属性获取
                        question_id = item.get("id") or ""
                        if not question_id and item.has_attr("name"):
                            # 尝试从name属性获取
                            question_id = item.get("name") or ""

                    # 如果仍然没有ID，尝试从子元素获取
                    if not question_id:
                        # 查找带有ID的子元素
                        id_elem = item.select_one("[id]")
                        if id_elem:
                            question_id = id_elem.get("id") or ""

                    # 如果仍然没有ID，生成一个随机ID
                    if not question_id:
                        question_id = f"q_{len(question_data) + 1}"

                    # 获取题目类型
                    question_type = 4  # 默认为简答题
                    type_span = item.select_one(".colorShallow")
                    if type_span:
                        type_text = (
                            type_span.text.strip().replace("(", "").replace(")", "")
                        )
                        if "单选" in type_text:
                            question_type = 0
                        elif "多选" in type_text:
                            question_type = 1
                        elif "判断" in type_text:
                            question_type = 3
                        elif "填空" in type_text:
                            question_type = 2
                        elif "简答" in type_text or "主观" in type_text:
                            question_type = 4

                    # 获取题目内容
                    # 首先尝试获取题目文本
                    content_elem = item.select_one(".mark_name")
                    if not content_elem:
                        content_elem = item.select_one(".Zy_TItle")
                    if not content_elem:
                        content_elem = item

                    # 获取题目HTML内容
                    question_html = str(content_elem)

                    # 获取题目文本内容 - 修改这部分以排除题型前缀
                    question_text = ""
                    if content_elem:
                        # 检查是否有题型前缀元素
                        type_span = content_elem.select_one(".colorShallow")
                        if type_span:
                            # 移除题型前缀元素后获取文本
                            type_span.extract()  # 从DOM中移除题型前缀元素
                            question_text = content_elem.text.strip()
                        else:
                            # 如果没有找到题型前缀元素，尝试通过正则表达式清理
                            question_text = content_elem.text.strip()
                            # 移除类似"(单选题, 2分)"的前缀
                            question_text = re.sub(
                                r"^\s*\(\s*(?:单选题|多选题|判断题|填空题|简答题)[\s\,\.]*\d*分?\s*\)\s*",
                                "",
                                question_text,
                            )
                    else:
                        question_text = f"题目 {len(question_data) + 1}"

                    # 清除题目末尾的分数标记，如"(5.0)"
                    question_text = re.sub(
                        r"\s*\(\s*\d+(\.\d+)?\s*\)\s*$", "", question_text
                    )

                    # 处理题目中的特殊字符
                    question_text = question_text.replace("\n", " ").replace("\t", " ")
                    while "  " in question_text:
                        question_text = question_text.replace("  ", " ")

                    # 获取选项（如果有）
                    options = {}
                    option_items = item.select(".clearfix li")
                    for i, option in enumerate(option_items):
                        option_label = chr(65 + i)  # A, B, C, ...
                        option_text = option.text.strip()
                        options[option_label] = option_text

                    # 将题目信息添加到数据中
                    question_data[question_id] = {
                        "text": question_html,  # 使用HTML内容
                        "plain_text": question_text,  # 纯文本内容
                        "type": question_type,
                        "options": options,
                    }
                    question_ids.append(question_id)

                    # 添加每个题目的答案类型到表单数据
                    if form_data:
                        form_data[f"answertype{question_id}"] = str(question_type)

                except Exception as e:
                    logger.error(f"ID:{self.username},解析题目异常: {str(e)}")

            # 添加answerwqbid字段
            if form_data and question_ids:
                form_data["answerwqbid"] = ",".join(question_ids) + ","

            logger.info(f"ID:{self.username},成功提取到 {len(question_data)} 个问题")
            return form_data, question_ids, question_data

        except Exception as e:
            logger.error(f"ID:{self.username},提取表单数据和问题异常: {str(e)}")
            traceback.print_exc()
            return {}, [], {}

    def _submit_homework_answers(self, form_data, question_ids, answers, homework_url):
        """提交作业答案

        Args:
            form_data: 表单数据
            question_ids: 问题ID列表
            answers: 答案字典
            homework_url: 作业URL

        Returns:
            提交结果
        """
        try:
            # 构建提交数据，严格按照batch_fill_homework.py的实现
            submit_data = form_data.copy()

            # 添加答案到表单数据
            for question_id, answer in answers.items():
                if not answer:
                    continue

                # 清理答案内容，处理特殊字符和格式
                cleaned_answer = answer

                # 如果答案中包含HTML标签，提取纯文本
                if "<" in cleaned_answer and ">" in cleaned_answer:
                    try:
                        # 使用BeautifulSoup提取纯文本
                        soup = BeautifulSoup(cleaned_answer, "html.parser")
                        cleaned_answer = soup.get_text()
                    except Exception:
                        # 如果解析失败，使用正则表达式移除HTML标签
                        cleaned_answer = re.sub(r"<.*?>", "", cleaned_answer)

                # 处理答案中可能存在的"###"符号
                if "###" in cleaned_answer:
                    cleaned_answer = cleaned_answer.replace("###", "")

                # 将处理后的答案添加到表单
                submit_data[f"answer{question_id}"] = cleaned_answer

                # 确保answertype字段存在且正确
                submit_data[f"answertype{question_id}"] = "4"  # 默认作为简答题处理

            # 构建提交URL，严格按照batch_fill_homework.py的实现
            url = "https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNewWeb"

            # 添加URL参数，严格按照batch_fill_homework.py的格式
            class_id = submit_data.get("classId", "")
            course_id = submit_data.get("courseId", "")
            token = submit_data.get("enc_work", "")
            total_question_num = submit_data.get(
                "totalQuestionNum", "6eaca1bf84b6b650bc4b09d8f8805f1e"
            )

            url += f"?_classId={class_id}&courseid={course_id}&token={token}&totalQuestionNum={total_question_num}"
            url += "&pyFlag=1&ua=pc&formType=post&saveStatus=1&version=1"

            logger.info(f"ID:{self.username},提交作业答案到URL: {url}")
            logger.info(
                f"ID:{self.username},题目数量: {len(question_ids)}, 已填写答案数量: {len(answers)}"
            )

            # 设置请求头，严格按照batch_fill_homework.py的格式
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Accept": "application/json, text/javascript, */*; q=0.01",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "Pragma": "no-cache",
                "Cache-Control": "no-cache",
                "sec-ch-ua-platform": '"Windows"',
                "X-Requested-With": "XMLHttpRequest",
                "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                "sec-ch-ua-mobile": "?0",
                "Origin": "https://mooc1.chaoxing.com",
                "Sec-Fetch-Site": "same-origin",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Dest": "empty",
                "Referer": homework_url,
                "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            }

            # 发送提交请求
            response = self.session.post(
                url,
                data=submit_data,
                headers=headers,
            )

            # 检查提交结果
            try:
                result_json = json.loads(response.text)
                if result_json.get("status") or result_json.get("status") == True:
                    logger.success(
                        f"ID:{self.username},作业提交成功: {result_json.get('msg', '')}"
                    )
                    return True
                else:
                    error_msg = result_json.get("msg", "未知错误")
                    logger.warning(f"ID:{self.username},作业提交失败: {error_msg}")

                    # 如果是因为答案格式问题，尝试备用提交方式
                    if (
                        "格式" in error_msg
                        or "无效的参数" in error_msg
                        or "code-2" in error_msg
                    ):
                        logger.info(f"ID:{self.username},尝试备用提交方式")

                        # 备用提交方式1: 使用mooc2/work/submit接口
                        alt_url = (
                            "https://mooc1.chaoxing.com/mooc-ans/mooc2/work/submit"
                        )

                        # 准备备用提交数据
                        alt_data = {
                            "courseId": submit_data.get("courseId", ""),
                            "classId": submit_data.get("classId", ""),
                            "workId": submit_data.get("workRelationId", ""),
                            "answerId": submit_data.get("workAnswerId", ""),
                            "enc": submit_data.get("enc_work", ""),
                            "standardEnc": submit_data.get("standardEnc", ""),
                            "cpi": submit_data.get("cpi", ""),
                            "saveStatus": "1",
                            "version": "1",
                        }

                        # 添加答案，确保格式正确
                        for question_id, answer in answers.items():
                            if not answer:
                                continue
                            # 使用纯文本答案
                            alt_data[f"answer{question_id}"] = re.sub(
                                r"<.*?>", "", answer
                            ).replace("###", "")
                            # 设置答案类型为简答题
                            alt_data[f"answertype{question_id}"] = "4"

                        logger.info(f"ID:{self.username},尝试备用提交方式: {alt_url}")

                        alt_response = self.session.post(
                            alt_url,
                            data=alt_data,
                            headers=headers,
                        )

                        if alt_response.status_code == 200:
                            try:
                                alt_result = json.loads(alt_response.text)
                                if alt_result.get("status"):
                                    logger.success(
                                        f"ID:{self.username},备用提交成功: {alt_result.get('msg', '')}"
                                    )
                                    return True
                            except:
                                if (
                                    "保存成功" in alt_response.text
                                    or "提交成功" in alt_response.text
                                ):
                                    logger.success(f"ID:{self.username},备用提交成功")
                                    return True

                        # 备用提交方式2: 使用saveStudentAnswerInfo接口
                        final_url = "https://mooc1.chaoxing.com/mooc-ans/work/saveStudentAnswerInfo"
                        logger.info(
                            f"ID:{self.username},尝试最终备用提交方式: {final_url}"
                        )

                        # 准备最终备用提交数据
                        final_data = alt_data.copy()
                        # 添加必要的参数
                        final_data["pyFlag"] = "1"
                        final_data["mooc2"] = "1"

                        final_response = self.session.post(
                            final_url,
                            data=final_data,
                            headers=headers,
                        )

                        if final_response.status_code == 200:
                            try:
                                final_result = json.loads(final_response.text)
                                if final_result.get("status"):
                                    logger.success(
                                        f"ID:{self.username},最终备用提交成功: {final_result.get('msg', '')}"
                                    )
                                    return True
                            except:
                                if (
                                    "保存成功" in final_response.text
                                    or "提交成功" in final_response.text
                                ):
                                    logger.success(
                                        f"ID:{self.username},最终备用提交成功"
                                    )
                                    return True
            except json.JSONDecodeError:
                # 如果不是JSON格式，尝试检查文本内容
                if (
                    "提交成功" in response.text
                    or "完成了整套试题" in response.text
                    or "保存成功" in response.text
                ):
                    logger.success(f"ID:{self.username},作业提交成功")
                    return True
                else:
                    logger.warning(
                        f"ID:{self.username},作业可能未成功提交，返回内容: {response.text[:100]}..."
                    )
                    return False

            return False
        except Exception as e:
            logger.error(f"ID:{self.username},提交答案异常: {str(e)}")
            traceback.print_exc()
            return False

    def process_course_homeworks(
        self, course_id, class_id=None, cpi=None, platform_id=None
    ):
        """处理指定课程的所有作业"""
        try:
            # 获取作业列表
            homeworks = self.get_homework_list_from_course(course_id, class_id, cpi)
            if not homeworks:
                logger.warning(f"ID:{self.username},未找到作业")
                return False

            logger.info(f"ID:{self.username},找到 {len(homeworks)} 个作业")

            # 处理每个作业
            success_count = 0
            for i, homework in enumerate(homeworks):
                logger.info(
                    f"ID:{self.username},处理第 {i+1}/{len(homeworks)} 个作业: {homework['title']}"
                )

                # 传递platform_id参数
                if self.process_homework(homework["url"], platform_id):
                    success_count += 1
                    logger.success(
                        f"ID:{self.username},作业 {homework['title']} 处理成功"
                    )
                else:
                    logger.error(
                        f"ID:{self.username},作业 {homework['title']} 处理失败"
                    )

            logger.info(
                f"ID:{self.username},共处理 {len(homeworks)} 个作业，成功 {success_count} 个"
            )
            return success_count > 0

        except Exception as e:
            logger.error(f"ID:{self.username},处理课程作业异常: {str(e)}")
            traceback.print_exc()
            return False

    def process_homework_by_url(self, homework_url, platform_id=None):
        """通过URL直接处理作业"""
        try:
            logger.info(f"ID:{self.username},通过URL直接处理作业: {homework_url}")
            # 传递platform_id参数
            return self.process_homework(homework_url, platform_id)
        except Exception as e:
            logger.error(f"ID:{self.username},处理作业异常: {str(e)}")
            traceback.print_exc()
            return False
