2025-08-04 17:34:14.913 | INFO     | __main__:<module>:1888 - 学习通自动化系统启动...
2025-08-04 17:34:14.913 | SUCCESS  | Config.UserSql:__init__:22 - 数据库连接池初始化成功
2025-08-04 17:34:14.913 | SUCCESS  | __main__:<module>:1898 - 数据库连接池初始化成功
2025-08-04 17:34:15.446 | SUCCESS  | __main__:<module>:1919 - 数据状态重置完成
2025-08-04 17:34:15.447 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-08-04 17:34:15.447 | INFO     | __main__:<module>:1935 - 开始处理订单...
2025-08-04 17:34:15.447 | INFO     | __main__:order_get:1691 - 订单处理线程启动，最大线程数: 100
2025-08-04 17:34:15.893 | INFO     | __main__:order_get:1716 - 获取到 7 个待处理订单，当前活跃线程数: 0
2025-08-04 17:34:15.900 | INFO     | __main__:Run:1775 - 开始处理订单: OID=314949, 用户=***********, 课程ID=*********
2025-08-04 17:34:15.900 | INFO     | __main__:register_thread:67 - 注册线程: OID=314949, 当前活跃线程数: 1
2025-08-04 17:34:16.442 | SUCCESS  | __main__:Run:1819 - ID:***********,登录成功
2025-08-04 17:34:16.517 | INFO     | __main__:kclist:254 - ID:***********,正在获取课程列表...
2025-08-04 17:34:16.916 | INFO     | __main__:kclist:256 - ID:***********,课程列表API返回: {'result': 1, 'msg': '获取成功', 'channelList': [{'cfid': -1, 'norder': 12, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 480770067, 'key': 125174198, 'content': {'studentcount': 315, 'chatid': '287069441163267', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '2025年春季学期2025级专科学前教育一班001', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '335778', 'coursestate': 0, 'teacherfactor': '2025春季', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=254266575&personId=480770067&classId=125174198&userId=401206590', 'imageurl': 'http://p.ananas.chaoxing.com/star3/origin/669ca80d6a0c5f74835bb936a41aabca.jpg', 'name': '思想道德与法治', 'id': 254266575}]}, 'roletype': 3, 'id': 125174198, 'state': 0, 'cpi': 480770067, 'bbsid': 'a8bbd675d0a5a655d114740d49eb95e6', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 11, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 480770067, 'key': 125174201, 'content': {'studentcount': 315, 'chatid': '287069444308993', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '2025年春季学期2025级专科学前教育一班002', 'course': {'data': [{'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '335778', 'coursestate': 0, 'teacherfactor': '辛向阳 等', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=254266281&personId=480770067&classId=125174201&userId=401206590', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/b94f5ed229c7e3c7d0308b3e7adfcd93.jpg', 'name': '形势与政策（2025上半年）', 'id': 254266281}]}, 'roletype': 3, 'id': 125174201, 'state': 0, 'cpi': 480770067, 'bbsid': '95300940e3c8474f39820d9be426b92d', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 10, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 480770067, 'key': 125174232, 'content': {'studentcount': 315, 'chatid': '287069475766273', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '2025年春季学期2025级专科学前教育一班008', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '335778', 'coursestate': 0, 'teacherfactor': '哈尔滨工业大学', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=254266303&personId=480770067&classId=125174232&userId=401206590', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/53c66593a310a822ad8b315c.jpg', 'name': '大学英语（一）', 'id': 254266303}]}, 'roletype': 3, 'id': 125174232, 'state': 0, 'cpi': 480770067, 'bbsid': 'ede23b7a6df14939456352a3520780b8', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 9, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 480770067, 'key': 125174204, 'content': {'studentcount': 315, 'chatid': '287069448503301', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '2025年春季学期2025级专科学前教育一班003', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '335778', 'coursestate': 0, 'teacherfactor': '廖媛媛', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=254266284&personId=480770067&classId=125174204&userId=401206590', 'imageurl': 'https://p.ananas.chaoxing.com/star3/270_169c/09c60c059e2a734ac183f18fe3113a8a.jpg', 'name': '计算机基础', 'id': 254266284}]}, 'roletype': 3, 'id': 125174204, 'state': 0, 'cpi': 480770067, 'bbsid': '01dd069d2aab8bf2cb2dbe5a2b0a4a8b', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 8, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 480770067, 'key': *********, 'content': {'studentcount': 315, 'chatid': '287069450600450', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '2025年春季学期2025级专科学前教育一班004', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '335778', 'coursestate': 0, 'teacherfactor': '李雄鹰 等', 'isCourseSquare': 0, 'schools': '兰州大学', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=*********&personId=480770067&classId=*********&userId=401206590', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/0df99e41f9f16e523be37ff2519ae986.png', 'name': '心理健康教育', 'id': *********}]}, 'roletype': 3, 'id': *********, 'state': 0, 'cpi': 480770067, 'bbsid': 'e007fba2eb5b6e2d497bde9881e9e6ce', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 7, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 480770067, 'key': 125174233, 'content': {'studentcount': 221, 'chatid': '287069476814849', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '2025年春季学期2025级专科学前教育一班009', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '335778', 'coursestate': 0, 'teacherfactor': '柳倩 华东师范大学 副教授', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=254266304&personId=480770067&classId=125174233&userId=401206590', 'imageurl': 'https://p.ananas.chaoxing.com/star3/193_114c/f928b522aaf09fdc7fe9339a44213d33.jpg', 'name': '学前儿童卫生与保育', 'id': 254266304}]}, 'roletype': 3, 'id': 125174233, 'state': 0, 'cpi': 480770067, 'bbsid': 'dfaafa5674a8a5ad696cb522043548a3', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 6, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 480770067, 'key': 125174235, 'content': {'studentcount': 221, 'chatid': '287069477863428', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '2025年春季学期2025级专科学前教育一班010', 'course': {'data': [{'appInfo': '6', 'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '335778', 'coursestate': 0, 'teacherfactor': '曹贵康', 'isCourseSquare': 0, 'schools': '《普通心理学》是心理学的一门必修的专业基础课，学习心理学一般应从普通心理学入手。本课程也常被称为“基础心理学”、“心理学概论”和“心理学导论”等。', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=254266305&personId=480770067&classId=125174235&userId=401206590', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/6ce77a10dd3268daa7ba6c93e5e76459.png', 'name': '普通心理学', 'id': 254266305}]}, 'roletype': 3, 'id': 125174235, 'state': 0, 'cpi': 480770067, 'bbsid': '877dc1512eee0cff8ca5b3cad2fe261f', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 5, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 480770067, 'key': 125174236, 'content': {'studentcount': 221, 'chatid': '287069478912001', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '2025年春季学期2025级专科学前教育一班011', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '335778', 'coursestate': 0, 'teacherfactor': '周海宏', 'isCourseSquare': 0, 'schools': '中央音乐学院', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=254266306&personId=480770067&classId=125174236&userId=401206590', 'imageurl': 'https://p.ananas.chaoxing.com/star2/2014-06/270_169c/1403710729258icdgl.jpg', 'name': '音乐（一）', 'id': 254266306}]}, 'roletype': 3, 'id': 125174236, 'state': 0, 'cpi': 480770067, 'bbsid': '28fd27e86f58b68b01242c2043e1b43a', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 4, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 480770067, 'key': 125174237, 'content': {'studentcount': 221, 'chatid': '287069481009153', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '2025年春季学期2025级专科学前教育一班012', 'course': {'data': [{'appInfo': '6', 'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '335778', 'coursestate': 0, 'teacherfactor': '代璐', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=254266307&personId=480770067&classId=125174237&userId=401206590', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/6ce77a10dd3268daa7ba6c93e5e76459.png', 'name': '美术（一）', 'id': 254266307}]}, 'roletype': 3, 'id': 125174237, 'state': 0, 'cpi': 480770067, 'bbsid': 'b169843b58b1479aac143b56bb831ce8', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 3, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 480770067, 'key': 125174211, 'content': {'studentcount': 315, 'chatid': '287069455843329', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '2025年春季学期2025级专科学前教育一班005', 'course': {'data': [{'appInfo': '6', 'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '335778', 'coursestate': 0, 'teacherfactor': '方芳', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=254266290&personId=480770067&classId=125174211&userId=401206590', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/6f6f9ea6a6a2ed83ab20c0e63eee533d.jpg', 'name': '入学教育', 'id': 254266290}]}, 'roletype': 3, 'id': 125174211, 'state': 0, 'cpi': 480770067, 'bbsid': 'c85ddd34eaa4bf4de986088625b30dd4', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 2, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 480770067, 'key': 125174276, 'content': {'studentcount': 315, 'chatid': '287069757833217', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '2025年春季学期2025级专科学前教育一班006', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '335778', 'coursestate': 0, 'teacherfactor': '2025春季', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=254266577&personId=480770067&classId=125174276&userId=401206590', 'imageurl': 'http://p.ananas.chaoxing.com/star3/origin/669ca80d6a0c5f74835bb936a41aabca.jpg', 'name': '认知实习', 'id': 254266577}]}, 'roletype': 3, 'id': 125174276, 'state': 0, 'cpi': 480770067, 'bbsid': 'c2cb03f64c37e1af9394fdd47df18fce', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 1, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 480770067, 'key': 125174217, 'content': {'studentcount': 315, 'chatid': '287069461086210', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '2025年春季学期2025级专科学前教育一班007', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '335778', 'coursestate': 0, 'teacherfactor': '曹冬梅、聂长久 吉林大学', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=254266282&personId=480770067&classId=125174217&userId=401206590', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/2dce31558a8c0df25a0bda3f99853bf3.jpg', 'name': '新中国史（四史课）', 'id': 254266282}]}, 'roletype': 3, 'id': 125174217, 'state': 0, 'cpi': 480770067, 'bbsid': 'ccbc150a11f4744f912d6ce19d990e4e', 'isSquare': 0}, 'topsign': 0}], 'mcode': '-1', 'createcourse': 1, 'teacherEndCourse': 0, 'showEndCourse': 0, 'hasMore': False, 'stuEndCourse': 0}
2025-08-04 17:34:16.919 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 254266575, 目标课程ID: *********
2025-08-04 17:34:16.919 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 254266281, 目标课程ID: *********
2025-08-04 17:34:16.919 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 254266303, 目标课程ID: *********
2025-08-04 17:34:16.919 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 254266284, 目标课程ID: *********
2025-08-04 17:34:16.920 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: *********, 目标课程ID: *********
2025-08-04 17:34:16.920 | SUCCESS  | __main__:kclist:298 - ID:***********,成功匹配课程: {'kcname': '心理健康教育', 'courseid': '*********', 'clazzid': *********, 'cpi': 480770067}
2025-08-04 17:34:16.920 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 254266304, 目标课程ID: *********
2025-08-04 17:34:16.920 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 254266305, 目标课程ID: *********
2025-08-04 17:34:16.921 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 254266306, 目标课程ID: *********
2025-08-04 17:34:16.921 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 254266307, 目标课程ID: *********
2025-08-04 17:34:16.921 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 254266290, 目标课程ID: *********
2025-08-04 17:34:16.921 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 254266577, 目标课程ID: *********
2025-08-04 17:34:16.921 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 254266282, 目标课程ID: *********
2025-08-04 17:34:16.922 | SUCCESS  | __main__:Run:1828 - ID:***********,课程信息匹配成功
2025-08-04 17:34:17.436 | SUCCESS  | __main__:studentcourse:757 - ID:***********,课件获取完成，总共获取到49个未完成章节
2025-08-04 17:34:18.130 | ERROR    | data.Porgres:get_xxt_jxz:120 - 解析视频信息失败: 'NoneType' object has no attribute 'text'
2025-08-04 17:34:19.707 | SUCCESS  | __main__:studentstudy:935 - ID:***********,进度:2%,详情:课程任务:0/97 | 章节测验: 0/48;   平均分;0 分; | 实时执行：1.1 什么是心理健康 (1/49) | 更新:2025-08-04 17:34:17
2025-08-04 17:34:20.901 | INFO     | __main__:order_get:1731 - 账号 *********** 已有任务在运行，跳过
2025-08-04 17:34:20.973 | INFO     | __main__:order_get:1731 - 账号 *********** 已有任务在运行，跳过
2025-08-04 17:34:21.047 | INFO     | __main__:order_get:1731 - 账号 *********** 已有任务在运行，跳过
2025-08-04 17:34:21.119 | INFO     | __main__:order_get:1731 - 账号 *********** 已有任务在运行，跳过
2025-08-04 17:34:21.192 | INFO     | __main__:order_get:1731 - 账号 *********** 已有任务在运行，跳过
2025-08-04 17:34:21.287 | INFO     | __main__:order_get:1731 - 账号 *********** 已有任务在运行，跳过
2025-08-04 17:34:57.714 | SUCCESS  | API.TaskDo:task:181 - ID:***********,准备执行测试
2025-08-04 17:34:58.665 | INFO     | API.WorkTask:Html_Wkrk:54 - ID:***********,当前平台ID: 0
2025-08-04 17:34:58.667 | INFO     | API.WorkTask:Html_Wkrk:253 - ID:***********,找到4个题目
2025-08-04 17:34:58.667 | INFO     | API.WorkTask:Html_Wkrk:259 - ID:***********,开始处理第 1/4 个题目
2025-08-04 17:34:58.670 | INFO     | API.WorkTask:Html_Wkrk:457 - ID:***********,尝试使用主题库查询答案
2025-08-04 17:34:58.670 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [多选题]下列哪几项属于心理健康的衡量标准()?...
2025-08-04 17:34:59.115 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 生活目标切合实际情况###具有一定的学习能力###保持个性的完整与和谐###保持良好的人际关系...
2025-08-04 17:34:59.117 | INFO     | API.WorkTask:Xuan:972 - ID:***********,找到选项: {'A': 'A\xa0\xa0生活目标切合实际情况', 'B': 'B具有一定的学习能力', 'C': 'C\xa0\xa0保持个性的完整与和谐', 'D': 'D\xa0\xa0保持良好的人际关系'}
2025-08-04 17:34:59.118 | INFO     | API.WorkTask:Xuan:973 - ID:***********,题库答案: 生活目标切合实际情况###具有一定的学习能力###保持个性的完整与和谐###保持良好的人际关系
2025-08-04 17:34:59.118 | INFO     | API.WorkTask:Xuan:1054 - ID:***********,多选题部分包含匹配: A - 生活目标切合实际情况
2025-08-04 17:34:59.118 | INFO     | API.WorkTask:Xuan:1054 - ID:***********,多选题部分包含匹配: B - 具有一定的学习能力
2025-08-04 17:34:59.118 | INFO     | API.WorkTask:Xuan:1054 - ID:***********,多选题部分包含匹配: C - 保持个性的完整与和谐
2025-08-04 17:34:59.118 | INFO     | API.WorkTask:Xuan:1054 - ID:***********,多选题部分包含匹配: D - 保持良好的人际关系
2025-08-04 17:34:59.118 | SUCCESS  | API.WorkTask:Xuan:1078 - ID:***********,多选题匹配成功: ABCD
2025-08-04 17:34:59.120 | SUCCESS  | API.WorkTask:Html_Wkrk:486 - ID:***********,主题库答案匹配成功: ABCD
2025-08-04 17:34:59.120 | INFO     | API.WorkTask:Html_Wkrk:543 - ID:***********,已处理 1/4 个题目
2025-08-04 17:34:59.120 | INFO     | API.WorkTask:Html_Wkrk:259 - ID:***********,开始处理第 2/4 个题目
2025-08-04 17:34:59.121 | INFO     | API.WorkTask:Html_Wkrk:457 - ID:***********,尝试使用主题库查询答案
2025-08-04 17:34:59.121 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]21世纪,成为人才的首要条件是()?...
2025-08-04 17:34:59.477 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 心理健康...
2025-08-04 17:34:59.479 | INFO     | API.WorkTask:Xuan:972 - ID:***********,找到选项: {'A': 'A身体健康', 'B': 'B心理健康', 'C': 'C社会适应良好', 'D': 'D人际关系良好'}
2025-08-04 17:34:59.479 | INFO     | API.WorkTask:Xuan:973 - ID:***********,题库答案: 心理健康
2025-08-04 17:34:59.479 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:***********,答案与选项内容包含匹配: B
2025-08-04 17:34:59.479 | SUCCESS  | API.WorkTask:Html_Wkrk:486 - ID:***********,主题库答案匹配成功: B
2025-08-04 17:34:59.480 | INFO     | API.WorkTask:Html_Wkrk:543 - ID:***********,已处理 2/4 个题目
2025-08-04 17:34:59.480 | INFO     | API.WorkTask:Html_Wkrk:259 - ID:***********,开始处理第 3/4 个题目
2025-08-04 17:34:59.481 | INFO     | API.WorkTask:Html_Wkrk:457 - ID:***********,尝试使用主题库查询答案
2025-08-04 17:34:59.482 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]西方最早论述心理学的著作是()?...
2025-08-04 17:35:00.059 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 《论灵魂》...
2025-08-04 17:35:00.061 | INFO     | API.WorkTask:Xuan:972 - ID:***********,找到选项: {'A': 'A《梦的解析》', 'B': 'B《论灵魂》', 'C': 'C《理想国》', 'D': 'D《记忆论》'}
2025-08-04 17:35:00.061 | INFO     | API.WorkTask:Xuan:973 - ID:***********,题库答案: 《论灵魂》
2025-08-04 17:35:00.062 | SUCCESS  | API.WorkTask:Xuan:1103 - ID:***********,答案与选项内容包含匹配: B
2025-08-04 17:35:00.062 | SUCCESS  | API.WorkTask:Html_Wkrk:486 - ID:***********,主题库答案匹配成功: B
2025-08-04 17:35:00.062 | INFO     | API.WorkTask:Html_Wkrk:543 - ID:***********,已处理 3/4 个题目
2025-08-04 17:35:00.062 | INFO     | API.WorkTask:Html_Wkrk:259 - ID:***********,开始处理第 4/4 个题目
2025-08-04 17:35:00.063 | INFO     | API.WorkTask:Html_Wkrk:457 - ID:***********,尝试使用主题库查询答案
2025-08-04 17:35:00.064 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [判断题]盲目追星反映出了心理学中的角色效应。( )...
2025-08-04 17:35:00.402 | WARNING  | __main__:signal_handler:104 - 接收到终止信号，正在安全关闭...
2025-08-04 17:35:00.440 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 错...
2025-08-04 17:35:00.441 | SUCCESS  | API.WorkTask:Html_Wkrk:515 - ID:***********,主题库判断题答案: false
2025-08-04 17:35:00.441 | INFO     | API.WorkTask:Html_Wkrk:543 - ID:***********,已处理 4/4 个题目
2025-08-04 17:35:00.441 | INFO     | API.WorkTask:Html_Wkrk:839 - ID:***********,所有题目处理完成，共 4/4 个
2025-08-04 17:35:00.908 | SUCCESS  | API.WorkTask:PostDo:1502 - ID:***********,提交答案成功: {"msg":"success!","stuStatus":4,"backUrl":"","url":"/mooc-ans/work/phone/work-relation?workId=f82f2de849444ee2b478005e32162f6d&relationId=********&courseId=*********&clazzId=*********&knowledgeId=**********&mooc=0&jobId=work-423da563b5e54069a7bd9d36dc948bfb&enc=fdb0c381e7948fa42e266d39d58ac3d5&ut=s&originJobId=null","status":true}
2025-08-04 17:35:06.411 | INFO     | __main__:studentstudy:1121 - ID:***********,章节 1.1 什么是心理健康 完成，等待 67 秒后处理下一章节
