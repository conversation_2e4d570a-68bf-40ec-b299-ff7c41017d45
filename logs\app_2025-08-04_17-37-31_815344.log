2025-08-04 17:37:31.816 | INFO     | __main__:<module>:1888 - 学习通自动化系统启动...
2025-08-04 17:37:31.816 | SUCCESS  | Config.UserSql:__init__:22 - 数据库连接池初始化成功
2025-08-04 17:37:31.816 | SUCCESS  | __main__:<module>:1898 - 数据库连接池初始化成功
2025-08-04 17:37:31.818 | SUCCESS  | __main__:<module>:1919 - 数据状态重置完成
2025-08-04 17:37:31.818 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-08-04 17:37:31.818 | INFO     | __main__:<module>:1935 - 开始处理订单...
2025-08-04 17:37:31.819 | INFO     | __main__:order_get:1691 - 订单处理线程启动，最大线程数: 100
2025-08-04 17:37:31.820 | INFO     | __main__:order_get:1716 - 获取到 1 个待处理订单，当前活跃线程数: 0
2025-08-04 17:37:31.821 | INFO     | __main__:Run:1775 - 开始处理订单: OID=300009, 用户=15992092132, 课程ID=246729504
2025-08-04 17:37:31.821 | INFO     | __main__:register_thread:67 - 注册线程: OID=300009, 当前活跃线程数: 1
2025-08-04 17:37:32.285 | SUCCESS  | __main__:Run:1819 - ID:15992092132,登录成功
2025-08-04 17:37:32.287 | INFO     | __main__:kclist:254 - ID:15992092132,正在获取课程列表...
2025-08-04 17:37:32.777 | INFO     | __main__:kclist:256 - ID:15992092132,课程列表API返回: {'result': 1, 'msg': '获取成功', 'channelList': [{'cfid': -1, 'norder': 11, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 434790031, 'key': 124806372, 'content': {'studentcount': 1240, 'chatid': '285324558270465', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '24级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '186199', 'coursestate': 0, 'teacherfactor': '张亮 胡星铭 邵佳德', 'isCourseSquare': 0, 'schools': '南京大学', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=246729504&personId=434790031&classId=124806372&userId=365478185', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/2fd9a753c4cfa30da05ac7c47ede4b22.jpg', 'name': '走近中华优秀传统文化', 'id': 246729504}]}, 'roletype': 3, 'id': 124806372, 'state': 0, 'cpi': 434790031, 'bbsid': 'ddea376c8a25df1f6264313b9a88bc75', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 10, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 434790031, 'key': 123962473, 'content': {'studentcount': 55, 'chatid': '283905777270785', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '24药学4班', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '0', 'coursestate': 0, 'teacherfactor': '蔡文莹', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=249095876&personId=434790031&classId=123962473&userId=365478185', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/3a7c4e2929905646967168696f78ff01.png', 'name': '药用基础化学', 'id': 249095876}]}, 'roletype': 3, 'id': 123962473, 'state': 0, 'cpi': 434790031, 'bbsid': '0f978f6f85c962cacd78fddd0f36e3f5', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 9, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 434790031, 'key': 122312090, 'content': {'studentcount': 53, 'chatid': '281441739014147', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '24药学4班', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '186199', 'coursestate': 0, 'teacherfactor': '陈思烁、黄烁佳、杨冰璇、林远茂', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=252994520&personId=434790031&classId=122312090&userId=365478185', 'imageurl': 'http://p.ananas.chaoxing.com/star3/origin/669ca80d6a0c5f74835bb936a41aabca.jpg', 'name': '病理学与病理生理学（药学、医检、医影）', 'id': 252994520}]}, 'roletype': 3, 'id': 122312090, 'state': 0, 'cpi': 434790031, 'bbsid': '6c56763c8ea7bb7abba728e6b33b64d2', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 7, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 434790031, 'key': 114667653, 'content': {'studentcount': 53, 'chatid': '272939646779395', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '药学4班', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '186199', 'coursestate': 0, 'teacherfactor': '陈曼娜', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=249813736&personId=434790031&classId=114667653&userId=365478185', 'imageurl': 'http://p.ananas.chaoxing.com/star3/240_130c/ee1c91e5d692ad719f7edd5c90a3625e.jpg', 'name': '病原生物与免疫学', 'id': 249813736}]}, 'roletype': 3, 'id': 114667653, 'state': 0, 'cpi': 434790031, 'bbsid': '03f8af75cab7443a3de22db7d3bd3fec', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 6, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 434790031, 'key': 115927053, 'content': {'studentcount': 53, 'chatid': '273629452500994', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '24药学4班', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '186199', 'coursestate': 0, 'teacherfactor': '吴中华', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=245404188&personId=434790031&classId=115927053&userId=365478185', 'imageurl': 'http://p.ananas.chaoxing.com/star3/origin/669ca80d6a0c5f74835bb936a41aabca.jpg', 'name': '医学统计学202409', 'id': 245404188}]}, 'roletype': 3, 'id': 115927053, 'state': 0, 'cpi': 434790031, 'bbsid': 'ad70e4e6d022b3129b5c9ef5d6af7945', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 5, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 434790031, 'key': 114964585, 'content': {'studentcount': 53, 'chatid': '273055389646849', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '24药学4班', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '137305', 'coursestate': 0, 'teacherfactor': '王晶', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=250028327&personId=434790031&classId=114964585&userId=365478185', 'imageurl': 'http://p.ananas.chaoxing.com/star3/origin/669ca80d6a0c5f74835bb936a41aabca.jpg', 'name': '24级药学班-生物化学', 'id': 250028327}]}, 'roletype': 3, 'id': 114964585, 'state': 0, 'cpi': 434790031, 'bbsid': '44d64eb90d5b2ea1ac90a8ae2c360201', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 3, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 434790031, 'key': 111314082, 'content': {'studentcount': 53, 'chatid': '266314818060289', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '默认班级', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '186199', 'coursestate': 0, 'teacherfactor': '黄伟楠', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=248489064&personId=434790031&classId=111314082&userId=365478185', 'imageurl': 'http://p.ananas.chaoxing.com/star3/origin/669ca80d6a0c5f74835bb936a41aabca.jpg', 'name': '职业英语 24药学4班', 'id': 248489064}]}, 'roletype': 3, 'id': 111314082, 'state': 0, 'cpi': 434790031, 'bbsid': 'f7a5ed946194dab841b68400de46b86e', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 2, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 434790031, 'key': 108101771, 'content': {'studentcount': 1151, 'chatid': '262422447325187', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '2024级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '186199', 'coursestate': 0, 'teacherfactor': '付建利 韩刚 丁雪明 孙华', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=246729505&personId=434790031&classId=108101771&userId=365478185', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/81fe5ee18c59595ce39d0ee4dc41f1d8.png', 'name': '剑指CET-4：大学生英语能力基础', 'id': 246729505}]}, 'roletype': 3, 'id': 108101771, 'state': 0, 'cpi': 434790031, 'bbsid': '42d5e9be730ff2610bbbc1f0fd751226', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 8, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 434790031, 'key': 115882698, 'content': {'studentcount': 53, 'chatid': '273591468883972', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '24药学4班', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '186199', 'coursestate': 1, 'teacherfactor': '陈佳', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=250342514&personId=434790031&classId=115882698&userId=365478185', 'imageurl': 'http://p.ananas.chaoxing.com/star3/origin/883735c8bbf5a4c61381f1ed96b40c9d.jpg', 'name': '24药学《中医药学概论》', 'id': 250342514}]}, 'roletype': 3, 'id': 115882698, 'state': 1, 'cpi': 434790031, 'bbsid': 'f69cc55b497080d6d1dc8ce3b8df0bcf', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 4, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 434790031, 'key': 107278741, 'content': {'studentcount': 1247, 'chatid': '261597106864133', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '186199', 'coursestate': 0, 'teacherfactor': '张国清', 'isCourseSquare': 0, 'schools': '同济大学', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=246729502&personId=434790031&classId=107278741&userId=365478185', 'imageurl': 'https://p.ananas.chaoxing.com/star3/270_169c/f01bc30632e023f83b3e8879cdeea2c7.jpg', 'name': '军事理论', 'id': 246729502}]}, 'roletype': 3, 'id': 107278741, 'state': 1, 'cpi': 434790031, 'bbsid': 'b06d54dda678283b1baa0909f74d124e', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 1, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 434790031, 'key': 107278743, 'content': {'studentcount': 1251, 'chatid': '261597107912706', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '2024级', 'course': {'data': [{'appInfo': '5,6', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '186199', 'coursestate': 0, 'teacherfactor': '中国人民警察大学', 'isCourseSquare': 0, 'schools': '牛继承 等', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=246729503&personId=434790031&classId=107278743&userId=365478185', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/c52ca84bf94b3aca4e1b63a1960a2186.png', 'name': '当代大学生国家安全教育', 'id': 246729503}]}, 'roletype': 3, 'id': 107278743, 'state': 1, 'cpi': 434790031, 'bbsid': 'da2fdd7fcbb50014293a0e5a83d53526', 'isSquare': 0}, 'topsign': 0}], 'mcode': '-1', 'createcourse': 1, 'teacherEndCourse': 0, 'showEndCourse': 0, 'hasMore': False, 'stuEndCourse': 0}
2025-08-04 17:37:32.780 | INFO     | __main__:kclist:286 - ID:15992092132,找到课程ID: 246729504, 目标课程ID: 246729504
2025-08-04 17:37:32.780 | SUCCESS  | __main__:kclist:298 - ID:15992092132,成功匹配课程: {'kcname': '走近中华优秀传统文化', 'courseid': '246729504', 'clazzid': 124806372, 'cpi': 434790031}
2025-08-04 17:37:32.781 | INFO     | __main__:kclist:286 - ID:15992092132,找到课程ID: 249095876, 目标课程ID: 246729504
2025-08-04 17:37:32.781 | INFO     | __main__:kclist:286 - ID:15992092132,找到课程ID: 252994520, 目标课程ID: 246729504
2025-08-04 17:37:32.781 | INFO     | __main__:kclist:286 - ID:15992092132,找到课程ID: 249813736, 目标课程ID: 246729504
2025-08-04 17:37:32.781 | INFO     | __main__:kclist:286 - ID:15992092132,找到课程ID: 245404188, 目标课程ID: 246729504
2025-08-04 17:37:32.782 | INFO     | __main__:kclist:286 - ID:15992092132,找到课程ID: 250028327, 目标课程ID: 246729504
2025-08-04 17:37:32.782 | INFO     | __main__:kclist:286 - ID:15992092132,找到课程ID: 248489064, 目标课程ID: 246729504
2025-08-04 17:37:32.782 | INFO     | __main__:kclist:286 - ID:15992092132,找到课程ID: 246729505, 目标课程ID: 246729504
2025-08-04 17:37:32.782 | INFO     | __main__:kclist:286 - ID:15992092132,找到课程ID: 250342514, 目标课程ID: 246729504
2025-08-04 17:37:32.782 | INFO     | __main__:kclist:286 - ID:15992092132,找到课程ID: 246729502, 目标课程ID: 246729504
2025-08-04 17:37:32.783 | INFO     | __main__:kclist:286 - ID:15992092132,找到课程ID: 246729503, 目标课程ID: 246729504
2025-08-04 17:37:32.783 | SUCCESS  | __main__:Run:1828 - ID:15992092132,课程信息匹配成功
2025-08-04 17:37:33.283 | SUCCESS  | __main__:studentcourse:757 - ID:15992092132,课件获取完成，总共获取到0个未完成章节
2025-08-04 17:37:33.952 | ERROR    | data.Porgres:get_xxt_jxz:120 - 解析视频信息失败: 'NoneType' object has no attribute 'text'
2025-08-04 17:37:35.263 | INFO     | __main__:unregister_thread:74 - 注销线程: OID=300009, 当前活跃线程数: 0
2025-08-04 17:38:31.819 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-08-04 17:39:31.820 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-08-04 17:39:46.404 | WARNING  | __main__:signal_handler:104 - 接收到终止信号，正在安全关闭...
2025-08-04 17:39:46.405 | INFO     | __main__:<module>:1945 - 程序退出
