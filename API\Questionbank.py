import re
import traceback
from collections import OrderedDict

from loguru import logger
import requests
import urllib3
from API import Re  # 导入Re模块


def contains_uppercase_letters(s):
    # 使用正则表达式匹配大写字母 A-Z
    pattern = r"[A-Z0-9]"
    # re.search() 方法查找字符串中是否存在匹配的字符
    return bool(re.search(pattern, s))


def remove_digits_and_symbols(text):
    # 使用正则表达式去掉所有的数字 (0-9) 和顿号 (、)
    modified_text = re.sub(r"[0-9、]", "", text)
    modified_text = re.sub(r"\s+", " ", modified_text).strip()
    return modified_text


def replace_src(a):
    a = a.replace("src", "xsrc")
    return a


def tiankong_quchong(text):
    """
    去除填空题中的重复答案，保持答案的唯一性和顺序
    :param text: 原始答案文本
    :return: 去重后的答案文本
    """
    try:
        # 使用OrderedDict保持顺序的同时去重
        if text:
            # 按逗号或分号分割
            parts = re.split(r"[,，;；]", text)
            # 去除空白项并去重，保持原有顺序
            unique_parts = list(
                OrderedDict.fromkeys([part.strip() for part in parts if part.strip()])
            )
            # 重新用逗号连接
            return ",".join(unique_parts)
        return text
    except:
        return text


# 禁用警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


def questionbank(question, answertype=0, timeout=10, platform_id=None):
    """
    查询题库获取答案，按照主题库 -> 备用题库的优先级顺序进行查询
    :param question: 问题文本
    :param answertype: 题目类型，0=选择题，1=判断题，2=填空题，3=简答题
    :param timeout: 查询超时时间（秒），默认10秒
    :param platform_id: 平台ID，用于控制日志输出
    :return: 答案或None
    """
    # 清理问题文本，使用Re.strip_title处理以保留填空符号
    # 检查是否包含填空符号
    has_blank_brackets = False
    if re.search(r"（\s+）|（\s*）|\(\s+\)|\(\s*\)", question):
        has_blank_brackets = True
        # 使用保留填空符号的方法处理题目
        question = Re.strip_title(question)
        # 仅在DEBUG级别输出调试信息
        if logger.level("DEBUG").no <= logger.level("INFO").no:
            logger.debug(
                f"题目包含填空符号，使用保留填空符号的方法处理: {question[:50]}..."
            )
    else:
        # 使用原来的方法处理题目
        question = remove_digits_and_symbols(question)
        question = question.strip()

    if not question:
        return None

    # 判断是否需要减少日志输出
    reduce_logs = platform_id == 9004

    # 尝试使用主题库
    try:
        # 仅在非9004平台ID时输出详细查询日志
        if not reduce_logs:
            logger.info(f"尝试使用主题库查询: {question[:50]}...")

        # 构建查询URL - 使用新的API地址
        url = "http://tk.mixuelo.cc/api.php?act=query"

        # 构建请求数据 - 使用新的参数格式
        data = {
            "key": "zXPX828s29Kk7Yj2",
            "question": question,
            "type": str(answertype),  # 确保类型是字符串
        }

        # 设置请求头
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Content-Type": "application/x-www-form-urlencoded",
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Origin": "http://tk.mixuelo.cc",
            "Referer": "http://tk.mixuelo.cc/",
        }

        # 发送请求，使用指定的超时时间
        response = requests.post(url, data=data, headers=headers, timeout=timeout)

        # 检查响应状态码
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 1 and result.get("data"):
                # 新API返回格式可能是嵌套的
                if isinstance(result["data"], dict) and "answer" in result["data"]:
                    answer = result["data"].get("answer", "")
                else:
                    answer = result["data"]

                if answer:
                    # 仅在非9004平台ID时输出成功日志
                    if not reduce_logs:
                        logger.success(f"主题库查询成功: {answer[:50]}...")
                    return answer
                else:
                    # 仅在非9004平台ID时输出警告日志
                    if not reduce_logs:
                        logger.warning("主题库找到题目但答案无效")
                    return "未收录答案"
            else:
                # 仅在非9004平台ID时输出警告日志
                if not reduce_logs:
                    logger.warning(
                        f"主题库查询未找到答案: {result.get('message', '未知原因')}"
                    )
        else:
            # 仅在非9004平台ID时输出警告日志
            if not reduce_logs:
                logger.warning(f"主题库查询请求失败，状态码: {response.status_code}")
    except requests.exceptions.Timeout:
        # 仅在非9004平台ID时输出警告日志
        if not reduce_logs:
            logger.warning("主题库查询超时，尝试备用题库")
    except Exception as e:
        # 错误日志总是输出，无论平台ID
        logger.error(f"主题库查询异常: {str(e)}")

    # 尝试使用备用题库
    try:
        # 仅在非9004平台ID时输出详细查询日志
        if not reduce_logs:
            logger.info(f"尝试使用备用题库查询: {question[:50]}...")

        # 构建查询URL - 使用新的备用题库URL
        url = f"http://yx.yunxue.icu/api?token=admin&q={question}"

        # 设置请求头
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "application/json, text/javascript, */*; q=0.01",
        }

        # 发送GET请求，使用指定的超时时间
        response = requests.get(url, headers=headers, timeout=timeout)

        # 检查响应状态码
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 1 and result.get("data"):
                answer = result["data"]
                if answer:
                    # 仅在非9004平台ID时输出成功日志
                    if not reduce_logs:
                        logger.success(f"备用题库查询成功: {answer[:50]}...")
                    return answer
                else:
                    # 仅在非9004平台ID时输出警告日志
                    if not reduce_logs:
                        logger.warning("备用题库找到题目但答案无效")
                    return "未收录答案"
            else:
                # 仅在非9004平台ID时输出警告日志
                if not reduce_logs:
                    logger.warning(
                        f"备用题库查询未找到答案: {result.get('msg', '未知原因')}"
                    )
        else:
            # 仅在非9004平台ID时输出警告日志
            if not reduce_logs:
                logger.warning(f"备用题库查询请求失败，状态码: {response.status_code}")
    except requests.exceptions.Timeout:
        # 仅在非9004平台ID时输出警告日志
        if not reduce_logs:
            logger.warning("备用题库查询超时")
    except Exception as e:
        # 错误日志总是输出，无论平台ID
        logger.error(f"备用题库查询异常: {str(e)}")

    # 所有题库都未找到答案
    # 仅在非9004平台ID时输出警告日志
    if not reduce_logs:
        logger.warning("所有题库均未找到答案")
    return None


def questionbank2(question, timeout=10, platform_id=None):
    """
    备用题库查询函数，用于在主题库查询失败后尝试

    Args:
        question: 题目内容
        timeout: 查询超时时间（秒），默认10秒
        platform_id: 平台ID，用于控制日志输出

    Returns:
        (answer, qnum): 答案和题目编号
    """
    try:
        # 判断是否需要减少日志输出
        reduce_logs = platform_id == 9004

        # 清理问题文本，使用Re.strip_title处理以保留填空符号
        # 检查是否包含填空符号
        has_blank_brackets = False
        if re.search(r"（\s+）|（\s*）|\(\s+\)|\(\s*\)", question):
            has_blank_brackets = True
            # 使用保留填空符号的方法处理题目
            processed_question = Re.strip_title(question)
            # 仅在DEBUG级别输出调试信息
            if logger.level("DEBUG").no <= logger.level("INFO").no:
                logger.debug(
                    f"题目包含填空符号，使用保留填空符号的方法处理: {processed_question[:30]}..."
                )
        else:
            # 使用原来的方法处理题目
            processed_question = remove_digits_and_symbols(question)
            processed_question = processed_question.strip()

        # 仅在非9004平台ID时输出详细查询日志
        if not reduce_logs:
            logger.info(f"尝试使用备用题库2查询: {processed_question[:30]}...")

        # 备用题库配置
        url = f"http://yx.yunxue.icu/api?token=admin&q={processed_question}"

        # 设置请求头
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "application/json, text/javascript, */*; q=0.01",
        }

        # 发送GET请求，使用指定的超时时间
        response = requests.get(url, headers=headers, timeout=timeout)
        result = response.json()

        # 检查响应
        if result.get("code") == 1 and result.get("data"):
            answer = result["data"]
            if answer and answer.strip():
                # 仅在非9004平台ID时输出成功日志
                if not reduce_logs:
                    logger.success(f"备用题库2查询成功: {answer[:30]}...")
                return answer, 3  # 使用固定值3表示备用题库2
            else:
                # 仅在非9004平台ID时输出警告日志
                if not reduce_logs:
                    logger.warning(f"备用题库2返回空答案")
                return "未收录答案", 3
        else:
            # 仅在非9004平台ID时输出警告日志
            if not reduce_logs:
                logger.warning(f"备用题库2查询失败: {result.get('msg', '未知原因')}")
    except requests.exceptions.Timeout:
        # 仅在非9004平台ID时输出警告日志
        if not reduce_logs:
            logger.warning(f"备用题库2查询超时")
    except Exception as e:
        # 错误日志总是输出，无论平台ID
        logger.warning(f"备用题库2查询异常: {str(e)}")

    # 如果查询失败，返回None
    return None, 0
