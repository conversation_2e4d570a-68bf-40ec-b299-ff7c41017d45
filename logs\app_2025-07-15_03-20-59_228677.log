2025-07-15 03:20:59.231 | INFO     | __main__:<module>:1888 - 学习通自动化系统启动...
2025-07-15 03:20:59.231 | SUCCESS  | Config.UserSql:__init__:22 - 数据库连接池初始化成功
2025-07-15 03:20:59.232 | SUCCESS  | __main__:<module>:1898 - 数据库连接池初始化成功
2025-07-15 03:20:59.236 | SUCCESS  | __main__:<module>:1919 - 数据状态重置完成
2025-07-15 03:20:59.241 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 0/100
2025-07-15 03:20:59.250 | INFO     | __main__:<module>:1935 - 开始处理订单...
2025-07-15 03:20:59.251 | INFO     | __main__:order_get:1691 - 订单处理线程启动，最大线程数: 100
2025-07-15 03:20:59.257 | INFO     | __main__:order_get:1716 - 获取到 1 个待处理订单，当前活跃线程数: 0
2025-07-15 03:20:59.258 | INFO     | __main__:Run:1775 - 开始处理订单: OID=300001, 用户=17328022636, 课程ID=246628486|107051161|352747128|42670108|6a76f288656266a67fb6a4820a3e7864|5ac17897c0eea6971f688e43b15b8f51
2025-07-15 03:20:59.258 | INFO     | __main__:register_thread:67 - 注册线程: OID=300001, 当前活跃线程数: 1
2025-07-15 03:20:59.921 | SUCCESS  | __main__:Run:1819 - ID:17328022636,登录成功
2025-07-15 03:20:59.923 | INFO     | __main__:kclist:225 - ID:17328022636,平台ID 9004，使用特殊处理方式获取作业信息
2025-07-15 03:20:59.924 | SUCCESS  | __main__:kclist:242 - ID:17328022636,成功构造作业课程信息: {'kcname': '作业任务', 'courseid': '246628486|107051161|352747128|42670108|6a76f288656266a67fb6a4820a3e7864|5ac17897c0eea6971f688e43b15b8f51', 'clazzid': '246628486|107051161|352747128|42670108|6a76f288656266a67fb6a4820a3e7864|5ac17897c0eea6971f688e43b15b8f51', 'cpi': ''}
2025-07-15 03:20:59.924 | SUCCESS  | __main__:Run:1828 - ID:17328022636,课程信息匹配成功
2025-07-15 03:20:59.926 | SUCCESS  | __main__:studentstudy:935 - ID:17328022636,进度:100%,详情:作业任务_42670108 | 进行中 | 进度: 1/1 | 更新: 2025-07-15 03:20:59
2025-07-15 03:21:01.216 | INFO     | __main__:_fallback_to_traditional_homework:1496 - ID:17328022636,获取到真实作业标题: 第十五章 第一二节
2025-07-15 03:21:01.907 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1394 - ID:17328022636,提取到基本表单字段: courseId=246628486, classId=107051161, workId=42670108
2025-07-15 03:21:01.911 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1405 - ID:17328022636,找到 22 个题目项
2025-07-15 03:21:01.915 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1535 - ID:17328022636,成功提取到 22 个问题
2025-07-15 03:21:01.915 | ERROR    | API.HomeworkAI:process_homework:1025 - ID:17328022636,未生成任何答案
2025-07-15 03:21:02.636 | INFO     | API.WorkTask:Html_Wkrk:185 - ID:17328022636,尝试解析作业页面参数
2025-07-15 03:21:14.612 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 科技
2025-07-15 03:21:14.614 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A教育', 'B': 'B科技', 'C': 'C人才', 'D': 'D创新'}
2025-07-15 03:21:14.615 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 科技
2025-07-15 03:21:14.616 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: B
2025-07-15 03:21:14.617 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 1/22 个题目
2025-07-15 03:21:26.513 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 科技
2025-07-15 03:21:26.517 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A教育', 'B': 'B科技', 'C': 'C人才', 'D': 'D创新'}
2025-07-15 03:21:26.518 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 科技
2025-07-15 03:21:26.519 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: B
2025-07-15 03:21:26.521 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 2/22 个题目
2025-07-15 03:21:38.436 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 教育
2025-07-15 03:21:38.438 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A教育', 'B': 'B科技', 'C': 'C人才', 'D': 'D创新'}
2025-07-15 03:21:38.439 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 教育
2025-07-15 03:21:38.439 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: A
2025-07-15 03:21:38.440 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 3/22 个题目
2025-07-15 03:21:50.409 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 科技
2025-07-15 03:21:50.411 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A科技', 'B': 'B人才', 'C': 'C创新', 'D': 'D教育'}
2025-07-15 03:21:50.412 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 科技
2025-07-15 03:21:50.412 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: A
2025-07-15 03:21:50.413 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 4/22 个题目
2025-07-15 03:21:59.252 | INFO     | __main__:monitor_thread:1929 - 当前活跃线程数: 1/100
2025-07-15 03:22:00.724 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A科技', 'B': 'B人才', 'C': 'C创新', 'D': 'D教育'}
2025-07-15 03:22:00.726 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 人才
2025-07-15 03:22:00.727 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: B
2025-07-15 03:22:00.728 | SUCCESS  | API.WorkTask:Html_Wkrk:486 - ID:17328022636,主题库答案匹配成功: B
2025-07-15 03:22:00.729 | INFO     | API.WorkTask:Html_Wkrk:543 - ID:17328022636,已处理 5/22 个题目
2025-07-15 03:22:13.395 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 创新
2025-07-15 03:22:13.397 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A科技', 'B': 'B人才', 'C': 'C创新', 'D': 'D教育'}
2025-07-15 03:22:13.398 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 创新
2025-07-15 03:22:13.398 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: C
2025-07-15 03:22:13.399 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 6/22 个题目
2025-07-15 03:22:25.896 | INFO     | API.WorkTask:get_ai_answer_for_choice:1707 - ID:17328022636,AI返回的答案内容: 教育优先发展
2025-07-15 03:22:25.897 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A创新驱动发展', 'B': 'B科技自立自强', 'C': 'C人才引领驱动', 'D': 'D教育优先发展'}
2025-07-15 03:22:25.898 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 教育优先发展
2025-07-15 03:22:25.898 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: D
2025-07-15 03:22:25.899 | INFO     | API.WorkTask:Html_Wkrk:786 - ID:17328022636,已处理 7/22 个题目
2025-07-15 03:22:36.162 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A创新驱动发展', 'B': 'B科技自立自强', 'C': 'C人才引领驱动', 'D': 'D教育优先发展'}
2025-07-15 03:22:36.163 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 科技自立自强
2025-07-15 03:22:36.164 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: B
2025-07-15 03:22:36.164 | SUCCESS  | API.WorkTask:Html_Wkrk:486 - ID:17328022636,主题库答案匹配成功: B
2025-07-15 03:22:36.165 | INFO     | API.WorkTask:Html_Wkrk:543 - ID:17328022636,已处理 8/22 个题目
2025-07-15 03:22:46.469 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A创新驱动发展', 'B': 'B科技自立自强', 'C': 'C人才引领驱动', 'D': 'D教育优先发展'}
2025-07-15 03:22:46.470 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 人才引领驱动
2025-07-15 03:22:46.470 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: C
2025-07-15 03:22:46.470 | SUCCESS  | API.WorkTask:Html_Wkrk:486 - ID:17328022636,主题库答案匹配成功: C
2025-07-15 03:22:46.470 | INFO     | API.WorkTask:Html_Wkrk:543 - ID:17328022636,已处理 9/22 个题目
2025-07-15 03:22:52.315 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A坚持党对教育工作的全面领导', 'B': 'B坚持社会主义教育方向', 'C': 'C坚持中国特色社会主义教育道路', 'D': 'D坚持立德树人根本任务'}
2025-07-15 03:22:52.316 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 加强党对教育工作的全面领导
2025-07-15 03:22:52.317 | INFO     | API.WorkTask:Xuan:1070 - ID:17328022636,尝试使用相似度匹配
2025-07-15 03:22:52.320 | INFO     | API.WorkTask:Xuan:1090 - ID:17328022636,尝试使用关键词匹配，答案关键词: {'加强党对教育工作的全面领导'}
2025-07-15 03:22:52.325 | INFO     | API.WorkTask:Xuan:1139 - ID:17328022636,尝试使用短语匹配，答案短语: ['加强党对教育工作的全面领导']
2025-07-15 03:22:52.325 | ERROR    | API.WorkTask:Xuan:1247 - ID:17328022636,使用match_answer函数匹配失败: match_answer() missing 2 required positional arguments: 'qtp' and 'username'
2025-07-15 03:22:52.326 | WARNING  | API.WorkTask:Xuan:1251 - ID:17328022636,答案无法与任何选项匹配
2025-07-15 03:22:52.326 | WARNING  | API.WorkTask:Html_Wkrk:548 - ID:17328022636,主题库答案处理失败，尝试备用题库
2025-07-15 03:22:52.603 | INFO     | API.WorkTask:Xuan:972 - ID:17328022636,找到选项: {'A': 'A坚持党对教育工作的全面领导', 'B': 'B坚持社会主义教育方向', 'C': 'C坚持中国特色社会主义教育道路', 'D': 'D坚持立德树人根本任务'}
2025-07-15 03:22:52.604 | INFO     | API.WorkTask:Xuan:973 - ID:17328022636,题库答案: 坚持党对教育工作的全面领导
2025-07-15 03:22:52.605 | SUCCESS  | API.WorkTask:Xuan:1031 - ID:17328022636,答案与选项内容包含匹配: A
2025-07-15 03:22:52.606 | SUCCESS  | API.WorkTask:Html_Wkrk:581 - ID:17328022636,备用题库答案匹配成功: A
2025-07-15 03:22:52.606 | INFO     | API.WorkTask:Html_Wkrk:635 - ID:17328022636,已处理 10/22 个题目
2025-07-15 03:22:57.394 | WARNING  | __main__:signal_handler:104 - 接收到终止信号，正在安全关闭...
