# 学习通自动化系统题库优化说明

## 主要问题

1. 主题库API无法使用，需要替换为新的API
2. 答案匹配机制不完善，无法正确匹配题库返回的答案与选项
3. 答题优先级顺序不明确，遇到一题使用题库查询不到后，就一直使用AI答题
4. 缺少答案匹配功能，导致题库返回的答案无法与选项正确匹配

## 解决方案

### 1. 题库API替换

- 主题库API替换为：`http://tk.mixuelo.cc/api.php?act=query`，使用POST请求，参数为：
  ```
  {
    "key": "zXPX828s29Kk7Yj2",
    "question": "问题文本"
  }
  ```
  
- 备用题库API保持不变：`http://yx.yunxue.icu/api?token=admin&q=问题文本`

### 2. 答案匹配机制优化

- 添加了答案文本与选项的匹配功能，通过以下两种方式进行匹配：
  1. 直接匹配：去除空格和标点后，检查答案是否包含在选项中，或选项是否包含在答案中
  2. 相似度匹配：使用difflib计算答案与选项的相似度，选择相似度最高且超过阈值的选项

### 3. 答题优先级顺序优化

- 实现了明确的答题优先级顺序：主题库 > 备用题库 > AI答题
- 每道题都会按照这个优先级顺序进行查询，而不是一旦某个题库失败就一直使用AI
- 添加了更详细的日志记录，方便调试和问题排查

### 4. 错误处理优化

- 增强了错误处理机制，确保即使某道题处理失败，也不会影响整个作业的提交
- 添加了超时设置，避免因网络问题导致系统卡住
- 在处理题目时添加了暂停机制，避免请求过于频繁

## 测试结果

创建了测试脚本 `test_questionbank.py` 验证题库查询和答案匹配逻辑，测试结果表明：

1. 主题库查询正常，能够返回正确的答案
2. 备用题库查询正常，能够返回正确的答案
3. 答案匹配功能正常，能够将题库返回的答案与选项正确匹配

## 注意事项

1. 平台ID 9004的处理方式与其他平台不同，需要特殊处理
2. 主题库和备用题库的返回格式不同，需要分别处理
3. 如果所有题库都查询失败，才会使用AI答题
4. 如果AI答题比例超过20%，会使用保存模式而不是提交模式，避免被系统检测 