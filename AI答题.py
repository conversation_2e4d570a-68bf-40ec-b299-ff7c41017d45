import requests
import json

url = "http://tk.mixuelo.cc/api.php?act=aimodel"

payload = {
    "key": "zXPX828s29Kk7Yj2",
    "question": "你是什么模型",
    "model": "deepseek-chat",
}

headers = {"Cookie": "PHPSESSID=th9rcnfi47nl9fhjsjrcbmvq01"}

print("发送请求到:", url)
print("请求头:", headers)
print("请求数据:", payload)

response = requests.post(url, data=payload, headers=headers)

print("\n状态码:", response.status_code)
print("响应头:", response.headers)

try:
    result = response.json()
    print("\n响应JSON:")
    print(json.dumps(result, indent=2, ensure_ascii=False))

    # 检查响应格式
    print("\n检查响应字段:")
    if "code" in result:
        print("code:", result["code"])
    if "msg" in result:
        print("msg:", result["msg"])
    if "data" in result:
        print("data字段存在，内容:", result["data"])
    if "answer" in result:
        print("answer字段存在，内容:", result["answer"])
except Exception as e:
    print("解析JSON失败:", str(e))
    print("原始响应:", response.text)
