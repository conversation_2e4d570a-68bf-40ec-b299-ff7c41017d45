import sys
import os
import traceback

print("Python版本:", sys.version)
print("当前工作目录:", os.getcwd())

try:
    print("尝试导入data.Porgres模块...")
    from data.Porgres import StartProces

    print("导入成功!")

    # 检查StartProces类的get_platform_9004_progress方法
    print("检查StartProces.get_platform_9004_progress方法...")

    # 创建一个模拟session对象
    class MockSession:
        def __init__(self):
            self.ai_status_info = "AI测试"

    session = MockSession()

    # 创建StartProces实例
    p = StartProces(
        session,
        [
            {
                "kcname": "测试课程",
                "courseid": "123456",
                "clazzid": "789012",
                "cpi": "345678",
            }
        ],
    )

    # 调用get_platform_9004_progress方法
    progress, remarks = p.get_platform_9004_progress(
        homework_title="测试作业", status="测试状态", current_step=1, total_steps=2
    )

    print("方法调用成功!")
    print("返回值:", progress, remarks)

except ImportError as e:
    print("导入错误:", str(e))
    traceback.print_exc()
except Exception as e:
    print("其他错误:", str(e))
    traceback.print_exc()

    # 尝试读取文件内容
    try:
        print("\n尝试读取data/Porgres.py文件内容...")
        with open("data/Porgres.py", "rb") as f:
            content = f.read()
            print("文件大小:", len(content), "字节")
            print("是否包含空字节:", b"\x00" in content)

            # 检查文件编码
            import chardet

            result = chardet.detect(content)
            print("检测到的编码:", result)

            # 尝试用不同编码读取
            encodings = ["utf-8", "gbk", "gb2312", "latin1"]
            for enc in encodings:
                try:
                    decoded = content.decode(enc)
                    print(f"使用 {enc} 编码可以成功解码")
                    # 检查是否有语法错误
                    try:
                        compile(decoded, "data/Porgres.py", "exec")
                        print(f"使用 {enc} 编码解码后的代码没有语法错误")
                    except SyntaxError as se:
                        print(f"使用 {enc} 编码解码后的代码有语法错误:", str(se))
                except UnicodeDecodeError:
                    print(f"使用 {enc} 编码无法解码")
    except Exception as e2:
        print("读取文件失败:", str(e2))
        traceback.print_exc()
